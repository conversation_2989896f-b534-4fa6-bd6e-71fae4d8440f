2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://198.18.0.1:8000/xiaozhi/v1/
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - HTTP服务地址: http://198.18.0.1:8003/
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 模拟WebSocket服务器启动在 0.0.0.0:8000
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 模拟HTTP服务器启动在端口 8003
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://198.18.0.1:8000/
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:09:52 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://198.18.0.1:8000/
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://198.18.0.1:8000/
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://198.18.0.1:8000/
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:17:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:17:45 - 1.0.0-rebuild_000000 - core.websocket_server - ERROR - core.websocket_server - WebSocket服务器启动失败: [Errno 48] error while attempting to bind on address ('0.0.0.0', 8000): address already in use
