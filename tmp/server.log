2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/xiaozhi/v1/
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - HTTP服务地址: http://**********:8003/
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 模拟WebSocket服务器启动在 0.0.0.0:8000
2025-06-07 12:10:03 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 模拟HTTP服务器启动在端口 8003
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:09:51 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:09:52 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:12:28 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:15:36 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:17:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:17:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:17:45 - 1.0.0-rebuild_000000 - core.websocket_server - ERROR - core.websocket_server - WebSocket服务器启动失败: [Errno 48] error while attempting to bind on address ('0.0.0.0', 8000): address already in use
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 14:19:18 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 14:19:19 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成（模拟）
2025-06-07 14:19:19 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器启动成功
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.auth - INFO - core.auth - 认证中间件初始化完成 - 启用: False, Token数量: 0, 白名单设备: 0
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接处理器初始化完成 - Session: 133fbb2e-64cd-4b29-91de-0557cbfbeee9
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 请求头解析完成 - Headers: {'host': 'localhost:8000', 'upgrade': 'websocket', 'connection': 'Upgrade', 'sec-websocket-key': '1gBSV5Pp1oTQeEn/b1EiaA==', 'sec-websocket-version': '13', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits', 'user-agent': 'Python/3.9 websockets/15.0.1', 'device-id': 'test-device-001', 'client-id': 'test-client-001'}
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接建立成功 - IP: 127.0.0.1, Device: test-device-001, Session: 133fbb2e-64cd-4b29-91de-0557cbfbeee9
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 欢迎消息已发送
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 开始初始化AI组件...
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: ping, Content: {"type": "ping", "timestamp": 1234567890}...
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u4f60\u597d\uff0c\u8fd9\u662f\u4e00\u6761\u6d4b\u8bd5\u6d88\u606f"}...
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 你好，这是一条测试消息
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到纯文本消息: 这是一条纯文本消息
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 这是一条纯文本消息
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接已关闭 - Session: 133fbb2e-64cd-4b29-91de-0557cbfbeee9, 持续时间: 0.00s, 消息数: 4
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 连接已移除 - 当前活跃连接数: 0
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.auth - INFO - core.auth - 认证中间件初始化完成 - 启用: False, Token数量: 0, 白名单设备: 0
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接处理器初始化完成 - Session: 8fc14ed2-5b2f-4c67-b0aa-7e977b12c8ab
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 客户端主动断开连接
2025-06-07 14:19:59 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 连接已移除 - 当前活跃连接数: 0
2025-06-07 14:20:00 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - AI组件初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - Provider管理器初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.vad.base - INFO - core.providers.vad.base - VAD Provider初始化: MockVAD
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - VAD Provider创建成功: MockVAD
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.asr.base - INFO - core.providers.asr.base - ASR Provider初始化: MockASR
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - ASR Provider创建成功: MockASR
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.llm.base - INFO - core.providers.llm.base - LLM Provider初始化: MockLLM
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - LLM Provider创建成功: MockLLM
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.tts.base - INFO - core.providers.tts.base - TTS Provider初始化: MockTTS
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - TTS Provider创建成功: MockTTS
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.vad.mock_vad - INFO - core.providers.vad.mock_vad - 开始初始化模拟VAD...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - 开始初始化模拟ASR...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - 开始初始化模拟LLM...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - 开始初始化模拟TTS...
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.vad.mock_vad - INFO - core.providers.vad.mock_vad - 模拟VAD初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - 模拟ASR初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - 模拟TTS初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - 模拟LLM初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - 所有AI Provider初始化成功
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块初始化完成
2025-06-07 15:15:44 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器启动成功
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.auth - INFO - core.auth - 认证中间件初始化完成 - 启用: False, Token数量: 0, 白名单设备: 0
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接处理器初始化完成 - Session: 43f15b2e-744c-43b5-9694-37ef0041b344
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 请求头解析完成 - Headers: {'host': 'localhost:8000', 'upgrade': 'websocket', 'connection': 'Upgrade', 'sec-websocket-key': 't0KHidjDSWCX49v5CHiGog==', 'sec-websocket-version': '13', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits', 'user-agent': 'Python/3.9 websockets/15.0.1', 'device-id': 'test-ai-pipeline', 'client-id': 'ai-test-001'}
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接建立成功 - IP: 127.0.0.1, Device: test-ai-pipeline, Session: 43f15b2e-744c-43b5-9694-37ef0041b344
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 欢迎消息已发送
2025-06-07 15:17:02 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 开始初始化AI组件...
2025-06-07 15:17:03 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - AI组件初始化完成
2025-06-07 15:17:03 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u4f60\u597d\uff0c\u4eca\u5929\u5929\u6c14\u600e\u4e48\u6837\uff1f"}...
2025-06-07 15:17:03 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 你好，今天天气怎么样？
2025-06-07 15:17:04 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u8bf7\u5e2e\u6211\u64ad\u653e\u4e00\u9996\u97f3\u4e50"}...
2025-06-07 15:17:04 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 请帮我播放一首音乐
2025-06-07 15:17:05 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u6211\u60f3\u4e86\u89e3\u4e00\u4e0b\u6700\u65b0\u7684\u65b0\u95fb"}...
2025-06-07 15:17:05 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 我想了解一下最新的新闻
2025-06-07 15:17:07 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - VAD检测到语音活动，准备进行ASR识别
2025-06-07 15:17:07 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - ASR识别完成 - 文本: 查询附近的餐厅, 置信度: 0.82, 音频长度: 1024 bytes
2025-06-07 15:17:07 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - ASR识别结果: 查询附近的餐厅
2025-06-07 15:17:07 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - LLM生成响应 - 用户输入: 查询附近的餐厅..., 响应长度: 30
2025-06-07 15:17:07 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - LLM响应完成: 我明白了，让我想想如何回答您。您提到了：查询附近的餐厅......
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - TTS合成完成 - 文本: 我明白了，让我想想如何回答您。您提到了：查询附近的餐厅......, 文件: tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_50b5af9e.wav
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - TTS合成完成: tmp/tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_50b5af9e.wav
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - VAD检测到语音活动，准备进行ASR识别
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - ASR识别完成 - 文本: 你好，我想了解一下天气情况, 置信度: 0.96, 音频长度: 1024 bytes
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - ASR识别结果: 你好，我想了解一下天气情况
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - LLM生成响应 - 用户输入: 你好，我想了解一下天气情况..., 响应长度: 15
2025-06-07 15:17:08 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - LLM响应完成: 今天可能会有小雨，记得带伞哦。...
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - TTS合成完成 - 文本: 今天可能会有小雨，记得带伞哦。..., 文件: tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_c581f300.wav
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - TTS合成完成: tmp/tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_c581f300.wav
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - VAD检测到语音活动，准备进行ASR识别
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - ASR识别完成 - 文本: 查询附近的餐厅, 置信度: 0.82, 音频长度: 1024 bytes
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - ASR识别结果: 查询附近的餐厅
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - LLM生成响应 - 用户输入: 查询附近的餐厅..., 响应长度: 30
2025-06-07 15:17:09 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - LLM响应完成: 我明白了，让我想想如何回答您。您提到了：查询附近的餐厅......
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - TTS合成完成 - 文本: 我明白了，让我想想如何回答您。您提到了：查询附近的餐厅......, 文件: tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_6fd73538.wav
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - TTS合成完成: tmp/tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_6fd73538.wav
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - VAD检测到语音活动，准备进行ASR识别
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - ASR识别完成 - 文本: 设置一个闹钟, 置信度: 0.85, 音频长度: 1024 bytes
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - ASR识别结果: 设置一个闹钟
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - LLM生成响应 - 用户输入: 设置一个闹钟..., 响应长度: 16
2025-06-07 15:17:10 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - LLM响应完成: 闹钟设置成功，到时间我会提醒您。...
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - TTS合成完成 - 文本: 闹钟设置成功，到时间我会提醒您。..., 文件: tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_1f9523da.wav
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - TTS合成完成: tmp/tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_1f9523da.wav
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - VAD检测到语音活动，准备进行ASR识别
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - ASR识别完成 - 文本: 播放轻音乐, 置信度: 0.92, 音频长度: 1024 bytes
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - ASR识别结果: 播放轻音乐
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - LLM生成响应 - 用户输入: 播放轻音乐..., 响应长度: 17
2025-06-07 15:17:11 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - LLM响应完成: 我推荐您听一些古典音乐，很放松的。...
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - TTS合成完成 - 文本: 我推荐您听一些古典音乐，很放松的。..., 文件: tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_89c42ce8.wav
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - TTS合成完成: tmp/tts_MockTTS_43f15b2e-744c-43b5-9694-37ef0041b344_89c42ce8.wav
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u4f60\u597d\uff0c\u6211\u662f\u5c0f\u660e"}...
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 你好，我是小明
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u6211\u60f3\u4e86\u89e3\u4e00\u4e0b\u4eba\u5de5\u667a\u80fd"}...
2025-06-07 15:17:12 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 我想了解一下人工智能
2025-06-07 15:17:13 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u90a3\u673a\u5668\u5b66\u4e60\u548c\u6df1\u5ea6\u5b66\u4e60\u6709\u4ec...
2025-06-07 15:17:13 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 那机器学习和深度学习有什么区别呢？
2025-06-07 15:17:15 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 收到文本消息 - Type: text, Content: {"type": "text", "content": "\u8c22\u8c22\u4f60\u7684\u89e3\u7b54"}...
2025-06-07 15:17:15 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 处理聊天文本: 谢谢你的解答
2025-06-07 15:17:27 - 1.0.0-rebuild_000000 - core.connection - INFO - core.connection - 连接已关闭 - Session: 43f15b2e-744c-43b5-9694-37ef0041b344, 持续时间: 24.21s, 消息数: 17
2025-06-07 15:17:27 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 连接已移除 - 当前活跃连接数: 0
2025-06-07 15:39:44 - 1.0.0-rebuild_000000 - plugins_func.register - INFO - plugins_func.register - 函数注册表初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - xiaozhi-server 重建版启动中...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - Provider管理器初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 插件加载器初始化完成 - 插件目录: plugins_func
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 插件管理器初始化完成 - 启用状态: True
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器初始化完成 - 监听地址: 0.0.0.0:8000
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - WebSocket地址: ws://**********:8000/
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 服务器启动完成，按 Ctrl+C 退出
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - 第二阶段：真实WebSocket服务器 + 连接处理 + 认证系统
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - __main__ - INFO - __main__ - ============================================================
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 启动WebSocket服务器在 0.0.0.0:8000
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - 开始初始化AI模块和插件系统...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.vad.base - INFO - core.providers.vad.base - VAD Provider初始化: MockVAD
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - VAD Provider创建成功: MockVAD
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.asr.base - INFO - core.providers.asr.base - ASR Provider初始化: MockASR
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - ASR Provider创建成功: MockASR
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.llm.base - INFO - core.providers.llm.base - LLM Provider初始化: MockLLM
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - LLM Provider创建成功: MockLLM
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.tts.base - INFO - core.providers.tts.base - TTS Provider初始化: MockTTS
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - TTS Provider创建成功: MockTTS
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.vad.mock_vad - INFO - core.providers.vad.mock_vad - 开始初始化模拟VAD...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - 开始初始化模拟ASR...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - 开始初始化模拟LLM...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - 开始初始化模拟TTS...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.vad.mock_vad - INFO - core.providers.vad.mock_vad - 模拟VAD初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.asr.mock_asr - INFO - core.providers.asr.mock_asr - 模拟ASR初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.tts.mock_tts - INFO - core.providers.tts.mock_tts - 模拟TTS初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.llm.mock_llm - INFO - core.providers.llm.mock_llm - 模拟LLM初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.providers.factory - INFO - core.providers.factory - 所有AI Provider初始化成功
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 开始初始化插件系统...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 开始加载所有插件...
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 插件加载完成 - 成功加载: 3 个插件
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 函数注册统计: {'total_functions': 0, 'function_names': [], 'type_distribution': {}}
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 插件系统初始化成功
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - plugins_func.loader - INFO - plugins_func.loader - 可用插件函数: []
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - AI模块和插件系统初始化完成
2025-06-07 15:39:45 - 1.0.0-rebuild_000000 - core.websocket_server - INFO - core.websocket_server - WebSocket服务器启动成功
