# xiaozhi-server 重建项目

## 项目概述
这是一个从零开始重建的xiaozhi-server AI语音助手后端系统，用于深度学习和理解原项目的架构设计。

## 学习目标
1. 深入理解异步WebSocket服务器架构
2. 掌握AI处理管道的设计模式
3. 学习插件系统的实现机制
4. 理解配置管理和日志系统

## 项目结构
```
xiaozhi-rebuild/
├── app.py                 # 主入口文件
├── config/                # 配置管理模块
│   ├── __init__.py
│   ├── settings.py        # 配置设置
│   ├── config_loader.py   # 配置加载器
│   └── logger.py          # 日志系统
├── core/                  # 核心模块
│   ├── __init__.py
│   ├── websocket_server.py # WebSocket服务器
│   ├── http_server.py     # HTTP服务器
│   ├── connection.py      # 连接处理器
│   ├── providers/         # AI服务提供者
│   ├── handle/           # 消息处理器
│   └── utils/            # 工具函数
├── plugins_func/         # 插件系统
├── data/                 # 数据目录
├── tmp/                  # 临时文件
├── models/               # AI模型
├── requirements.txt      # 依赖列表
└── config.yaml          # 配置文件
```

## 开发阶段
- [x] 第一阶段：项目启动与基础架构
- [ ] 第二阶段：连接处理与通信协议
- [ ] 第三阶段：AI处理管道
- [ ] 第四阶段：插件系统与功能扩展
- [ ] 第五阶段：配置管理与部署

## 运行方式
```bash
python app.py
```
