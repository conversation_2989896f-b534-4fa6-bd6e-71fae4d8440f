"""
设置管理模块
负责配置文件的验证和检查
"""
import os
from typing import Dict, Any
from .config_loader import load_config, get_project_dir

# 默认配置文件名
DEFAULT_CONFIG_FILE = "config.yaml"
config_file_valid = False


def check_config_file() -> None:
    """
    检查配置文件是否存在和有效
    
    Raises:
        FileNotFoundError: 配置文件不存在
        ValueError: 配置文件格式错误
    """
    global config_file_valid
    if config_file_valid:
        return
    
    project_dir = get_project_dir()
    default_config_path = os.path.join(project_dir, DEFAULT_CONFIG_FILE)
    custom_config_path = os.path.join(project_dir, "data", f".{DEFAULT_CONFIG_FILE}")
    
    # 检查默认配置文件
    if not os.path.exists(default_config_path):
        raise FileNotFoundError(
            f"找不到默认配置文件: {default_config_path}\n"
            "请确保项目根目录下存在 config.yaml 文件"
        )
    
    # 检查自定义配置文件（可选）
    if not os.path.exists(custom_config_path):
        print(f"提示：自定义配置文件不存在: {custom_config_path}")
        print("将使用默认配置，如需自定义配置请创建该文件")
    
    # 尝试加载配置以验证格式
    try:
        config = load_config()
        validate_config(config)
        config_file_valid = True
        print("配置文件检查通过")
    except Exception as e:
        raise ValueError(f"配置文件验证失败: {e}")


def validate_config(config: Dict[str, Any]) -> None:
    """
    验证配置文件的基本结构
    
    Args:
        config: 配置字典
        
    Raises:
        ValueError: 配置格式错误
    """
    required_sections = ["server", "log"]
    
    for section in required_sections:
        if section not in config:
            raise ValueError(f"配置文件缺少必需的节: {section}")
    
    # 验证服务器配置
    server_config = config.get("server", {})
    required_server_keys = ["ip", "port"]
    
    for key in required_server_keys:
        if key not in server_config:
            raise ValueError(f"服务器配置缺少必需的键: {key}")
    
    # 验证端口号
    try:
        port = int(server_config.get("port", 8000))
        if not (1 <= port <= 65535):
            raise ValueError(f"端口号无效: {port}，必须在1-65535之间")
    except (ValueError, TypeError):
        raise ValueError("端口号必须是有效的整数")
    
    # 验证日志配置
    log_config = config.get("log", {})
    log_level = log_config.get("log_level", "INFO")
    valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    
    if log_level not in valid_levels:
        raise ValueError(f"日志级别无效: {log_level}，必须是: {valid_levels}")


def get_server_config() -> Dict[str, Any]:
    """
    获取服务器配置
    
    Returns:
        Dict[str, Any]: 服务器配置
    """
    config = load_config()
    return config.get("server", {})


def get_log_config() -> Dict[str, Any]:
    """
    获取日志配置
    
    Returns:
        Dict[str, Any]: 日志配置
    """
    config = load_config()
    return config.get("log", {})


def is_debug_mode() -> bool:
    """
    检查是否为调试模式
    
    Returns:
        bool: 是否为调试模式
    """
    log_config = get_log_config()
    return log_config.get("log_level", "INFO") == "DEBUG"


def get_ai_config() -> Dict[str, Any]:
    """
    获取AI模块配置
    
    Returns:
        Dict[str, Any]: AI模块配置
    """
    config = load_config()
    return {
        "selected_module": config.get("selected_module", {}),
        "VAD": config.get("VAD", {}),
        "ASR": config.get("ASR", {}),
        "LLM": config.get("LLM", {}),
        "TTS": config.get("TTS", {}),
        "Memory": config.get("Memory", {}),
        "Intent": config.get("Intent", {}),
    }
