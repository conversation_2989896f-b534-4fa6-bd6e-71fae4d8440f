"""
日志系统模块
提供统一的日志配置和管理功能
"""
import os
import sys
from loguru import logger
from datetime import datetime

# 服务器版本
SERVER_VERSION = "1.0.0-rebuild"
_logger_initialized = False


def get_module_abbreviation(module_name, module_dict):
    """
    获取模块名称的缩写，如果为空则返回00
    如果名称中包含下划线，则返回下划线后面的前两个字符
    
    Args:
        module_name: 模块名称
        module_dict: 模块配置字典
        
    Returns:
        str: 模块缩写
    """
    module_value = module_dict.get(module_name, "")
    if not module_value:
        return "00"
    if "_" in module_value:
        parts = module_value.split("_")
        return parts[-1][:2] if parts[-1] else "00"
    return module_value[:2]


def build_module_string(selected_module):
    """
    构建模块字符串，用于日志标识
    
    Args:
        selected_module: 选中的模块配置
        
    Returns:
        str: 模块字符串
    """
    return (
        get_module_abbreviation("VAD", selected_module)
        + get_module_abbreviation("ASR", selected_module)
        + get_module_abbreviation("LLM", selected_module)
        + get_module_abbreviation("TTS", selected_module)
        + get_module_abbreviation("Memory", selected_module)
        + get_module_abbreviation("Intent", selected_module)
    )


def formatter(record):
    """
    为没有 tag 的日志添加默认值
    
    Args:
        record: 日志记录
        
    Returns:
        str: 格式化后的消息
    """
    record["extra"].setdefault("tag", record["name"])
    return record["message"]


def setup_logging(config=None):
    """
    设置日志系统
    
    Args:
        config: 配置字典，如果为None则使用默认配置
        
    Returns:
        logger: 配置好的日志器
    """
    global _logger_initialized
    
    # 默认配置
    if config is None:
        config = {
            "log": {
                "log_level": "INFO",
                "log_dir": "tmp",
                "log_file": "server.log",
                "log_format": "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>",
                "log_format_file": "{time:YYYY-MM-DD HH:mm:ss} - {version}_{selected_module} - {name} - {level} - {extra[tag]} - {message}",
                "selected_module": "000000"
            }
        }
    
    # 第一次初始化时配置日志
    if not _logger_initialized:
        log_config = config.get("log", {})
        selected_module_str = log_config.get("selected_module", "000000")
        
        # 配置日志额外信息
        logger.configure(extra={"selected_module": selected_module_str})
        
        # 获取日志格式
        log_format = log_config.get(
            "log_format",
            "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>"
        )
        log_format_file = log_config.get(
            "log_format_file",
            "{time:YYYY-MM-DD HH:mm:ss} - {version}_{selected_module} - {name} - {level} - {extra[tag]} - {message}"
        )
        
        # 替换版本和模块信息
        log_format = log_format.replace("{version}", SERVER_VERSION)
        log_format = log_format.replace("{selected_module}", selected_module_str)
        log_format_file = log_format_file.replace("{version}", SERVER_VERSION)
        log_format_file = log_format_file.replace("{selected_module}", selected_module_str)
        
        # 获取其他配置
        log_level = log_config.get("log_level", "INFO")
        log_dir = log_config.get("log_dir", "tmp")
        log_file = log_config.get("log_file", "server.log")
        
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            format=log_format,
            level=log_level,
            filter=formatter
        )
        
        # 添加文件输出
        log_file_path = os.path.join(log_dir, log_file)
        logger.add(
            log_file_path,
            format=log_format_file,
            level=log_level,
            filter=formatter,
            rotation="10 MB",      # 每个文件最大10MB
            retention="30 days",   # 保留30天
            compression=None,
            encoding="utf-8",
            enqueue=True,          # 异步安全
            backtrace=True,
            diagnose=True,
        )
        
        _logger_initialized = True
    
    return logger


def update_module_string(selected_module_str, config):
    """
    更新模块字符串并重新配置日志处理器
    
    Args:
        selected_module_str: 新的模块字符串
        config: 配置字典
    """
    current_module = logger._core.extra.get("selected_module", "")
    
    if current_module == selected_module_str:
        return
    
    try:
        # 重新设置日志
        global _logger_initialized
        _logger_initialized = False
        
        # 更新配置中的模块字符串
        if "log" not in config:
            config["log"] = {}
        config["log"]["selected_module"] = selected_module_str
        
        # 重新初始化日志
        setup_logging(config)
        
        logger.info(f"日志配置已更新，模块字符串: {selected_module_str}")
        
    except Exception as e:
        logger.error(f"日志配置更新失败: {str(e)}")
        raise
