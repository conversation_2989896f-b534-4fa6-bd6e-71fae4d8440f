"""
配置加载器模块
负责加载和合并各种配置文件
"""
import os
import yaml
from collections.abc import Mapping
from typing import Dict, Any, Optional

# 全局配置缓存
_config_cache: Optional[Dict[str, Any]] = None


def get_project_dir() -> str:
    """
    获取项目根目录
    
    Returns:
        str: 项目根目录路径
    """
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + "/"


def read_config(config_path: str) -> Dict[str, Any]:
    """
    读取YAML配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML格式错误
    """
    try:
        with open(config_path, "r", encoding="utf-8") as file:
            config = yaml.safe_load(file)
            return config if config is not None else {}
    except FileNotFoundError:
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"配置文件格式错误: {config_path}, 错误: {e}")


def merge_configs(default_config: Dict[str, Any], custom_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    递归合并配置，custom_config优先级更高
    
    Args:
        default_config: 默认配置
        custom_config: 用户自定义配置
        
    Returns:
        Dict[str, Any]: 合并后的配置
    """
    if not isinstance(default_config, Mapping) or not isinstance(custom_config, Mapping):
        return custom_config
    
    merged = dict(default_config)
    
    for key, value in custom_config.items():
        if (
            key in merged
            and isinstance(merged[key], Mapping)
            and isinstance(value, Mapping)
        ):
            merged[key] = merge_configs(merged[key], value)
        else:
            merged[key] = value
    
    return merged


def ensure_directories(config: Dict[str, Any]) -> None:
    """
    确保所有配置路径存在
    
    Args:
        config: 配置字典
    """
    dirs_to_create = set()
    project_dir = get_project_dir()
    
    # 基础目录
    basic_dirs = ["data", "tmp", "models"]
    for dir_name in basic_dirs:
        dirs_to_create.add(os.path.join(project_dir, dir_name))
    
    # 日志目录
    log_dir = config.get("log", {}).get("log_dir", "tmp")
    dirs_to_create.add(os.path.join(project_dir, log_dir))
    
    # 创建目录
    for dir_path in dirs_to_create:
        try:
            os.makedirs(dir_path, exist_ok=True)
        except PermissionError:
            print(f"警告：无法创建目录 {dir_path}，请检查写入权限")


def load_config() -> Dict[str, Any]:
    """
    加载配置文件
    支持多层配置覆盖：默认配置 <- 自定义配置
    
    Returns:
        Dict[str, Any]: 最终配置
    """
    global _config_cache
    if _config_cache is not None:
        return _config_cache
    
    project_dir = get_project_dir()
    default_config_path = os.path.join(project_dir, "config.yaml")
    custom_config_path = os.path.join(project_dir, "data", ".config.yaml")
    
    # 加载默认配置
    try:
        default_config = read_config(default_config_path)
    except FileNotFoundError:
        print(f"警告：默认配置文件不存在: {default_config_path}")
        default_config = {}
    
    # 加载自定义配置
    try:
        custom_config = read_config(custom_config_path)
    except FileNotFoundError:
        print(f"提示：自定义配置文件不存在: {custom_config_path}，将使用默认配置")
        custom_config = {}
    
    # 合并配置
    config = merge_configs(default_config, custom_config)
    
    # 确保目录存在
    ensure_directories(config)
    
    # 缓存配置
    _config_cache = config
    return config


def reload_config() -> Dict[str, Any]:
    """
    重新加载配置（清除缓存）
    
    Returns:
        Dict[str, Any]: 重新加载的配置
    """
    global _config_cache
    _config_cache = None
    return load_config()


def get_config_value(key_path: str, default_value: Any = None) -> Any:
    """
    获取配置值，支持点分隔的路径
    
    Args:
        key_path: 配置键路径，如 "server.port"
        default_value: 默认值
        
    Returns:
        Any: 配置值
        
    Example:
        >>> get_config_value("server.port", 8000)
        8000
    """
    config = load_config()
    keys = key_path.split(".")
    
    current = config
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default_value
    
    return current


def update_config_value(key_path: str, value: Any) -> None:
    """
    更新配置值（仅在内存中，不保存到文件）
    
    Args:
        key_path: 配置键路径，如 "server.port"
        value: 新值
    """
    global _config_cache
    if _config_cache is None:
        _config_cache = load_config()
    
    keys = key_path.split(".")
    current = _config_cache
    
    # 导航到父级
    for key in keys[:-1]:
        if key not in current:
            current[key] = {}
        current = current[key]
    
    # 设置值
    current[keys[-1]] = value
