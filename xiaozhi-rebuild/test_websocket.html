<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>xiaozhi-server WebSocket 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        
        .controls {
            margin: 20px 0;
        }
        input, button, textarea {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .sent { background-color: #e3f2fd; }
        .received { background-color: #f3e5f5; }
        .system { background-color: #fff3e0; }
        .error { background-color: #ffebee; color: #c62828; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 xiaozhi-server WebSocket 测试工具</h1>
        
        <div class="controls">
            <label>WebSocket地址:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8000/" style="width: 300px;">
            <br>
            <label>Device ID:</label>
            <input type="text" id="deviceId" value="test-device-001" placeholder="设备ID">
            <label>Client ID:</label>
            <input type="text" id="clientId" value="test-client-001" placeholder="客户端ID">
            <br>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button onclick="clearMessages()">清空日志</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="controls">
            <h3>发送消息测试</h3>
            <input type="text" id="messageInput" placeholder="输入文本消息" style="width: 400px;">
            <button onclick="sendTextMessage()" id="sendBtn" disabled>发送文本</button>
            <br>
            <button onclick="sendPing()" id="pingBtn" disabled>发送Ping</button>
            <button onclick="sendChatMessage()" id="chatBtn" disabled>发送聊天消息</button>
        </div>
        
        <h3>消息日志</h3>
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, className) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + className;
        }

        function addMessage(content, type = 'system') {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>[${timestamp}]</strong> ${content}`;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateButtons() {
            document.getElementById('connectBtn').disabled = isConnected;
            document.getElementById('disconnectBtn').disabled = !isConnected;
            document.getElementById('sendBtn').disabled = !isConnected;
            document.getElementById('pingBtn').disabled = !isConnected;
            document.getElementById('chatBtn').disabled = !isConnected;
        }

        function connect() {
            const wsUrl = document.getElementById('wsUrl').value;
            const deviceId = document.getElementById('deviceId').value;
            const clientId = document.getElementById('clientId').value;
            
            if (!deviceId) {
                alert('请输入Device ID');
                return;
            }

            updateStatus('连接中...', 'connecting');
            addMessage('正在连接到: ' + wsUrl, 'system');

            // 构建带参数的WebSocket URL
            const url = new URL(wsUrl);
            url.searchParams.set('device-id', deviceId);
            url.searchParams.set('client-id', clientId);

            try {
                ws = new WebSocket(url.toString());

                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    updateButtons();
                    addMessage('WebSocket连接已建立', 'system');
                };

                ws.onmessage = function(event) {
                    addMessage('收到: ' + event.data, 'received');
                    
                    // 尝试解析JSON消息
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'hello') {
                            addMessage(`收到欢迎消息 - Session: ${data.session_id}`, 'system');
                        } else if (data.type === 'pong') {
                            addMessage('收到Pong响应', 'system');
                        }
                    } catch (e) {
                        // 非JSON消息，忽略解析错误
                    }
                };

                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('连接已关闭', 'disconnected');
                    updateButtons();
                    addMessage(`连接关闭 - Code: ${event.code}, Reason: ${event.reason}`, 'system');
                };

                ws.onerror = function(error) {
                    addMessage('WebSocket错误: ' + error, 'error');
                    updateStatus('连接错误', 'disconnected');
                };

            } catch (error) {
                addMessage('连接失败: ' + error.message, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendTextMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (ws && isConnected) {
                ws.send(message);
                addMessage('发送: ' + message, 'sent');
                input.value = '';
            }
        }

        function sendPing() {
            if (ws && isConnected) {
                const pingMsg = {
                    type: 'ping',
                    timestamp: Date.now()
                };
                ws.send(JSON.stringify(pingMsg));
                addMessage('发送Ping消息', 'sent');
            }
        }

        function sendChatMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim() || '你好，这是一条测试消息';
            
            if (ws && isConnected) {
                const chatMsg = {
                    type: 'text',
                    content: message
                };
                ws.send(JSON.stringify(chatMsg));
                addMessage('发送聊天消息: ' + message, 'sent');
                input.value = '';
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateButtons();
            addMessage('WebSocket测试工具已就绪', 'system');
            
            // 回车键发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTextMessage();
                }
            });
        });
    </script>
</body>
</html>
