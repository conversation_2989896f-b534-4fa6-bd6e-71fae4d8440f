"""
xiaozhi-server 重建版主入口文件
实现异步服务器启动和优雅关闭机制
"""
import sys
import uuid
import signal
import asyncio
from typing import Optional

# 导入配置和日志模块
import os
import sys

# 确保导入当前目录的config模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from config.settings import check_config_file
from config.config_loader import load_config
from config.logger import setup_logging

# 导入核心服务器模块（稍后实现）
# from core.websocket_server import WebSocketServer
# from core.http_server import SimpleHttpServer

TAG = __name__


def get_local_ip() -> str:
    """
    获取本地IP地址
    
    Returns:
        str: 本地IP地址
    """
    import socket
    try:
        # 创建一个UDP socket连接到外部地址来获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"


async def wait_for_exit() -> None:
    """
    阻塞直到收到 Ctrl‑C / SIGTERM 信号
    支持跨平台的优雅关闭机制
    """
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        # 注册信号处理器
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        # Windows：等待 KeyboardInterrupt
        try:
            await asyncio.Future()  # 永远等待
        except KeyboardInterrupt:  # Ctrl‑C
            pass


async def monitor_stdin():
    """
    监控标准输入，处理用户输入
    这是一个简化版本，主要用于消费回车键输入
    """
    try:
        from aioconsole import ainput
        while True:
            await ainput()  # 异步等待输入
    except ImportError:
        # 如果没有安装 aioconsole，使用简单的等待
        while True:
            await asyncio.sleep(1)


class MockWebSocketServer:
    """
    模拟WebSocket服务器（第一阶段使用）
    """
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging(config)
    
    async def start(self):
        """启动WebSocket服务器"""
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("port", 8000))
        
        self.logger.bind(tag=TAG).info(f"模拟WebSocket服务器启动在 {host}:{port}")
        
        # 模拟服务器运行
        while True:
            await asyncio.sleep(1)


class MockHttpServer:
    """
    模拟HTTP服务器（第一阶段使用）
    """
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging(config)
    
    async def start(self):
        """启动HTTP服务器"""
        server_config = self.config["server"]
        port = int(server_config.get("http_port", 8003))
        
        self.logger.bind(tag=TAG).info(f"模拟HTTP服务器启动在端口 {port}")
        
        # 模拟服务器运行
        while True:
            await asyncio.sleep(1)


async def main():
    """
    主函数：启动所有服务并管理生命周期
    """
    # 检查配置文件
    try:
        check_config_file()
    except (FileNotFoundError, ValueError) as e:
        print(f"配置文件错误: {e}")
        return
    
    # 加载配置
    config = load_config()
    
    # 设置日志
    logger = setup_logging(config)
    logger.bind(tag=TAG).info("xiaozhi-server 重建版启动中...")
    
    # 生成认证密钥
    auth_key = config.get("manager-api", {}).get("secret", "")
    if not auth_key or len(auth_key) == 0:
        auth_key = str(uuid.uuid4().hex)
    config["server"]["auth_key"] = auth_key
    
    # 创建异步任务
    tasks = []
    
    # 添加 stdin 监控任务
    stdin_task = asyncio.create_task(monitor_stdin())
    tasks.append(stdin_task)
    
    # 启动 WebSocket 服务器（使用模拟版本）
    ws_server = MockWebSocketServer(config)
    ws_task = asyncio.create_task(ws_server.start())
    tasks.append(ws_task)
    
    # 启动 HTTP 服务器（使用模拟版本）
    http_server = MockHttpServer(config)
    http_task = asyncio.create_task(http_server.start())
    tasks.append(http_task)
    
    # 输出服务地址信息
    local_ip = get_local_ip()
    websocket_port = int(config["server"].get("port", 8000))
    http_port = int(config["server"].get("http_port", 8003))
    
    logger.bind(tag=TAG).info(f"WebSocket地址: ws://{local_ip}:{websocket_port}/xiaozhi/v1/")
    logger.bind(tag=TAG).info(f"HTTP服务地址: http://{local_ip}:{http_port}/")
    logger.bind(tag=TAG).info("=" * 60)
    logger.bind(tag=TAG).info("服务器启动完成，按 Ctrl+C 退出")
    logger.bind(tag=TAG).info("=" * 60)
    
    try:
        # 等待退出信号
        await wait_for_exit()
    except asyncio.CancelledError:
        logger.bind(tag=TAG).info("任务被取消，清理资源中...")
    finally:
        # 优雅关闭：取消所有任务
        logger.bind(tag=TAG).info("正在关闭服务器...")
        
        for task in tasks:
            task.cancel()
        
        # 等待任务终止（设置超时）
        if tasks:
            await asyncio.wait(
                tasks,
                timeout=3.0,
                return_when=asyncio.ALL_COMPLETED,
            )
        
        logger.bind(tag=TAG).info("服务器已关闭，程序退出")


if __name__ == "__main__":
    try:
        # 运行主程序
        asyncio.run(main())
    except KeyboardInterrupt:
        print("手动中断，程序终止")
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)
