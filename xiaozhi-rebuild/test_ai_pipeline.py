#!/usr/bin/env python3
"""
AI处理管道测试脚本
测试完整的VAD->ASR->LLM->TTS处理流程
"""
import asyncio
import json
import websockets
import random
from websockets.exceptions import ConnectionClosed

async def test_ai_pipeline():
    """测试完整的AI处理管道"""
    # WebSocket服务器地址
    uri = "ws://localhost:8000/?device-id=test-ai-pipeline&client-id=ai-test-001"
    
    print(f"🚀 开始测试AI处理管道")
    print(f"📡 连接到: {uri}")
    print("=" * 60)
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接建立成功!")
            
            # 等待欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {json.loads(welcome_msg)['type']}")
            
            # 等待AI组件初始化完成消息
            init_msg = await websocket.recv()
            init_data = json.loads(init_msg)
            if init_data.get("type") == "system" and init_data.get("status") == "ready":
                print("🤖 AI组件初始化完成")
            
            print("\n🎯 开始测试AI处理管道...")
            
            # 测试1: 文本消息处理（LLM）
            print("\n📝 测试1: 文本消息 -> LLM处理")
            await test_text_processing(websocket)
            
            # 测试2: 音频消息处理（VAD + ASR + LLM + TTS）
            print("\n🎵 测试2: 音频消息 -> 完整AI管道")
            await test_audio_processing(websocket)
            
            # 测试3: 多轮对话
            print("\n💬 测试3: 多轮对话测试")
            await test_multi_turn_conversation(websocket)
            
            print("\n✅ 所有测试完成!")
            
    except ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_text_processing(websocket):
    """测试文本处理"""
    test_messages = [
        "你好，今天天气怎么样？",
        "请帮我播放一首音乐",
        "我想了解一下最新的新闻"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"  📤 发送文本消息 {i}: {message}")
        
        # 发送文本消息
        text_msg = {
            "type": "text",
            "content": message
        }
        await websocket.send(json.dumps(text_msg))
        
        # 接收响应
        await receive_llm_responses(websocket)
        
        # 等待一下再发送下一条
        await asyncio.sleep(1)

async def test_audio_processing(websocket):
    """测试音频处理"""
    # 生成模拟音频数据
    audio_chunks = []
    for i in range(10):  # 生成10个音频块
        # 模拟音频数据（随机字节）
        audio_data = bytes([random.randint(0, 255) for _ in range(1024)])
        audio_chunks.append(audio_data)
    
    print(f"  📤 发送音频数据: {len(audio_chunks)} 个音频块")
    
    # 发送音频数据
    for i, audio_chunk in enumerate(audio_chunks):
        await websocket.send(audio_chunk)
        print(f"    📡 发送音频块 {i+1}/{len(audio_chunks)}")
        
        # 接收音频确认
        try:
            response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
            response_data = json.loads(response)
            if response_data.get("type") == "audio_ack":
                has_voice = response_data.get("has_voice", False)
                if has_voice:
                    print(f"    🎤 VAD检测到语音活动")
                    # 如果检测到语音，等待ASR和后续处理
                    await receive_ai_pipeline_responses(websocket)
                else:
                    print(f"    🔇 VAD未检测到语音")
        except asyncio.TimeoutError:
            print(f"    ⏰ 音频确认超时")
        except json.JSONDecodeError:
            print(f"    ⚠️  收到非JSON响应")
        
        # 短暂延迟模拟实时音频流
        await asyncio.sleep(0.1)

async def test_multi_turn_conversation(websocket):
    """测试多轮对话"""
    conversation = [
        "你好，我是小明",
        "我想了解一下人工智能",
        "那机器学习和深度学习有什么区别呢？",
        "谢谢你的解答"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"  💬 对话轮次 {i}: {message}")
        
        # 发送消息
        chat_msg = {
            "type": "text",
            "content": message
        }
        await websocket.send(json.dumps(chat_msg))
        
        # 接收响应
        await receive_llm_responses(websocket)
        
        # 等待一下再继续对话
        await asyncio.sleep(2)

async def receive_llm_responses(websocket):
    """接收LLM响应"""
    try:
        # 等待LLM开始响应
        start_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
        start_data = json.loads(start_msg)
        
        if start_data.get("type") == "llm_start":
            print(f"    🧠 LLM开始处理...")
            
            # 接收流式响应
            response_text = ""
            while True:
                try:
                    stream_msg = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    stream_data = json.loads(stream_msg)
                    
                    if stream_data.get("type") == "llm_stream":
                        token = stream_data.get("token", "")
                        response_text += token
                        print(f"    📝 LLM流式输出: {token}", end="", flush=True)
                    
                    elif stream_data.get("type") == "llm_complete":
                        complete_text = stream_data.get("text", "")
                        print(f"\n    ✅ LLM响应完成: {complete_text}")
                        
                        # 等待TTS结果
                        await receive_tts_response(websocket)
                        break
                        
                except asyncio.TimeoutError:
                    print(f"\n    ⏰ LLM响应超时")
                    break
                except json.JSONDecodeError:
                    print(f"\n    ⚠️  收到非JSON响应")
                    break
        else:
            print(f"    📨 收到其他响应: {start_data}")
            
    except asyncio.TimeoutError:
        print(f"    ⏰ 等待LLM响应超时")
    except json.JSONDecodeError:
        print(f"    ⚠️  LLM响应格式错误")

async def receive_tts_response(websocket):
    """接收TTS响应"""
    try:
        tts_msg = await asyncio.wait_for(websocket.recv(), timeout=3.0)
        tts_data = json.loads(tts_msg)
        
        if tts_data.get("type") == "tts_result":
            audio_file = tts_data.get("audio_file", "")
            text = tts_data.get("text", "")
            print(f"    🔊 TTS合成完成: {audio_file} (文本: {text[:30]}...)")
        else:
            print(f"    📨 收到其他TTS响应: {tts_data}")
            
    except asyncio.TimeoutError:
        print(f"    ⏰ 等待TTS响应超时")
    except json.JSONDecodeError:
        print(f"    ⚠️  TTS响应格式错误")

async def receive_ai_pipeline_responses(websocket):
    """接收完整AI管道的响应"""
    try:
        # 等待ASR结果
        asr_msg = await asyncio.wait_for(websocket.recv(), timeout=3.0)
        asr_data = json.loads(asr_msg)
        
        if asr_data.get("type") == "asr_result":
            recognized_text = asr_data.get("text", "")
            print(f"    🎯 ASR识别结果: {recognized_text}")
            
            # 继续接收LLM和TTS响应
            await receive_llm_responses(websocket)
        else:
            print(f"    📨 收到其他ASR响应: {asr_data}")
            
    except asyncio.TimeoutError:
        print(f"    ⏰ 等待ASR响应超时")
    except json.JSONDecodeError:
        print(f"    ⚠️  ASR响应格式错误")

async def main():
    """主函数"""
    print("🤖 xiaozhi-server AI处理管道测试工具")
    print("=" * 60)
    
    await test_ai_pipeline()
    
    print("=" * 60)
    print("🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
