"""
TTS (Text-to-Speech) Provider基类
语音合成的抽象接口
"""
import os
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, AsyncGenerator
from config.logger import setup_logging

TAG = __name__


class TTSProviderBase(ABC):
    """
    TTS Provider抽象基类
    定义语音合成的统一接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化TTS Provider
        
        Args:
            config: TTS配置字典
        """
        self.config = config
        self.logger = setup_logging()
        self.voice = config.get("voice", "default")
        self.output_dir = config.get("output_dir", "tmp")
        self.speed = config.get("speed", 1.0)
        self.pitch = config.get("pitch", 0)
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.logger.bind(tag=TAG).info(f"TTS Provider初始化: {self.__class__.__name__}")
    
    @abstractmethod
    async def text_to_speech(
        self, 
        text: str, 
        session_id: str, 
        **kwargs
    ) -> Optional[str]:
        """
        将文本转换为语音文件
        
        Args:
            text: 要合成的文本
            session_id: 会话ID
            **kwargs: 其他参数（如voice, speed等）
            
        Returns:
            Optional[str]: 生成的音频文件路径，失败返回None
        """
        pass
    
    async def text_to_speech_stream(
        self, 
        text: str, 
        session_id: str, 
        **kwargs
    ) -> AsyncGenerator[bytes, None]:
        """
        将文本转换为语音流（流式合成）
        
        Args:
            text: 要合成的文本
            session_id: 会话ID
            **kwargs: 其他参数
            
        Yields:
            bytes: 音频数据块
        """
        # 默认实现：先生成文件，再读取
        audio_file = await self.text_to_speech(text, session_id, **kwargs)
        if audio_file and os.path.exists(audio_file):
            try:
                with open(audio_file, "rb") as f:
                    while True:
                        chunk = f.read(4096)  # 4KB chunks
                        if not chunk:
                            break
                        yield chunk
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"读取音频文件失败: {e}")
            finally:
                # 清理临时文件
                try:
                    os.remove(audio_file)
                except Exception:
                    pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化TTS模型/服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.logger.bind(tag=TAG).info(f"TTS Provider清理资源: {self.__class__.__name__}")
    
    def generate_audio_filename(self, session_id: str, extension: str = "wav") -> str:
        """
        生成音频文件名
        
        Args:
            session_id: 会话ID
            extension: 文件扩展名
            
        Returns:
            str: 完整的文件路径
        """
        module_name = self.__class__.__name__
        file_name = f"tts_{module_name}_{session_id}_{uuid.uuid4().hex[:8]}.{extension}"
        return os.path.join(self.output_dir, file_name)
    
    def get_config(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        return self.config.get(key, default_value)
    
    def validate_text(self, text: str) -> str:
        """
        验证和清理文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text or not text.strip():
            return ""
        
        # 移除特殊字符，保留基本标点
        cleaned_text = text.strip()
        
        # 限制文本长度
        max_length = self.get_config("max_text_length", 1000)
        if len(cleaned_text) > max_length:
            cleaned_text = cleaned_text[:max_length] + "..."
            self.logger.bind(tag=TAG).warning(f"文本过长，已截断到{max_length}字符")
        
        return cleaned_text
