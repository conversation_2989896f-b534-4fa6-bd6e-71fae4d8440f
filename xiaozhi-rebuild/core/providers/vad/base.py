"""
VAD (Voice Activity Detection) Provider基类
语音活动检测的抽象接口
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from config.logger import setup_logging

TAG = __name__


class VADProviderBase(ABC):
    """
    VAD Provider抽象基类
    定义语音活动检测的统一接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化VAD Provider
        
        Args:
            config: VAD配置字典
        """
        self.config = config
        self.logger = setup_logging()
        self.logger.bind(tag=TAG).info(f"VAD Provider初始化: {self.__class__.__name__}")
    
    @abstractmethod
    async def is_vad(self, conn, audio_data: bytes) -> bool:
        """
        检测音频数据中的语音活动
        
        Args:
            conn: 连接对象
            audio_data: 音频数据（通常是Opus格式）
            
        Returns:
            bool: True表示检测到语音活动，False表示静音
        """
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化VAD模型
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.logger.bind(tag=TAG).info(f"VAD Provider清理资源: {self.__class__.__name__}")
    
    def get_config(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        return self.config.get(key, default_value)
