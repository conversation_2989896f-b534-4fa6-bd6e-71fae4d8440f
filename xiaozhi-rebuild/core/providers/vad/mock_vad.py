"""
模拟VAD实现
用于测试和演示VAD Provider的基本功能
"""
import asyncio
import random
from typing import Dict, Any
from .base import VADProviderBase

TAG = __name__


class MockVAD(VADProviderBase):
    """
    模拟VAD实现
    随机返回语音活动检测结果，用于测试
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模拟VAD
        
        Args:
            config: VAD配置字典
        """
        super().__init__(config)
        self.threshold = config.get("threshold", 0.5)
        self.voice_probability = config.get("voice_probability", 0.3)  # 30%概率检测到语音
        self.initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化VAD模型
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.bind(tag=TAG).info("开始初始化模拟VAD...")
            
            # 模拟初始化时间
            await asyncio.sleep(0.1)
            
            self.initialized = True
            self.logger.bind(tag=TAG).info("模拟VAD初始化完成")
            return True
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"模拟VAD初始化失败: {e}")
            return False
    
    async def is_vad(self, conn, audio_data: bytes) -> bool:
        """
        检测音频数据中的语音活动
        
        Args:
            conn: 连接对象
            audio_data: 音频数据
            
        Returns:
            bool: True表示检测到语音活动，False表示静音
        """
        if not self.initialized:
            await self.initialize()
        
        # 模拟VAD检测逻辑
        # 根据音频数据长度和随机因子决定是否有语音
        if len(audio_data) < 100:  # 太短的音频认为是静音
            return False
        
        # 使用随机数模拟VAD检测结果
        has_voice = random.random() < self.voice_probability
        
        if has_voice:
            self.logger.bind(tag=TAG).debug(
                f"VAD检测到语音活动 - 音频长度: {len(audio_data)} bytes"
            )
        else:
            self.logger.bind(tag=TAG).debug(
                f"VAD未检测到语音活动 - 音频长度: {len(audio_data)} bytes"
            )
        
        return has_voice
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.initialized = False
        await super().cleanup()
