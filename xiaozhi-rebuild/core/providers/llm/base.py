"""
LLM (Large Language Model) Provider基类
大语言模型的抽象接口
"""
from abc import ABC, abstractmethod
from typing import Generator, List, Dict, Any, Optional, Tuple
from config.logger import setup_logging

TAG = __name__


class LLMProviderBase(ABC):
    """
    LLM Provider抽象基类
    定义大语言模型的统一接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化LLM Provider
        
        Args:
            config: LLM配置字典
        """
        self.config = config
        self.logger = setup_logging()
        self.model_name = config.get("model_name", "default")
        self.max_tokens = config.get("max_tokens", 1000)
        self.temperature = config.get("temperature", 0.7)
        
        self.logger.bind(tag=TAG).info(f"LLM Provider初始化: {self.__class__.__name__}")
    
    @abstractmethod
    async def response(
        self, 
        session_id: str, 
        dialogue: List[Dict[str, str]], 
        **kwargs
    ) -> Generator[str, None, None]:
        """
        生成LLM响应（流式）
        
        Args:
            session_id: 会话ID
            dialogue: 对话历史，格式为[{"role": "user/assistant/system", "content": "..."}]
            **kwargs: 其他参数
            
        Yields:
            str: 响应文本片段
        """
        pass
    
    async def response_no_stream(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        **kwargs
    ) -> str:
        """
        生成LLM响应（非流式）
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户输入
            **kwargs: 其他参数
            
        Returns:
            str: 完整响应文本
        """
        try:
            # 构造对话格式
            dialogue = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = ""
            async for part in self.response("", dialogue, **kwargs):
                result += part
            
            return result
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM响应生成异常: {e}")
            return "【LLM服务响应异常】"
    
    async def response_with_functions(
        self, 
        session_id: str, 
        dialogue: List[Dict[str, str]], 
        functions: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[Tuple[str, Optional[Dict[str, Any]]], None, None]:
        """
        支持函数调用的LLM响应（流式）
        
        Args:
            session_id: 会话ID
            dialogue: 对话历史
            functions: 可用函数列表
            
        Yields:
            Tuple[str, Optional[Dict]]: (文本片段, 函数调用信息)
        """
        # 默认实现：不支持函数调用，只返回普通响应
        async for token in self.response(session_id, dialogue):
            yield token, None
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化LLM模型/服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.logger.bind(tag=TAG).info(f"LLM Provider清理资源: {self.__class__.__name__}")
    
    def get_config(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        return self.config.get(key, default_value)
    
    def build_system_prompt(self, base_prompt: str, **context) -> str:
        """
        构建系统提示词
        
        Args:
            base_prompt: 基础提示词
            **context: 上下文信息
            
        Returns:
            str: 完整的系统提示词
        """
        # 可以在这里添加通用的系统提示词逻辑
        # 比如添加时间、用户信息等上下文
        return base_prompt
