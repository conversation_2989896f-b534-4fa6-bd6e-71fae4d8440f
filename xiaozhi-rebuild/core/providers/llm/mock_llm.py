"""
模拟LLM实现
用于测试和演示LLM Provider的基本功能
"""
import asyncio
import random
from typing import Generator, List, Dict, Any
from .base import LLMProviderBase

TAG = __name__


class MockLLM(LLMProviderBase):
    """
    模拟LLM实现
    返回预设的智能回复，用于测试
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模拟LLM
        
        Args:
            config: LLM配置字典
        """
        super().__init__(config)
        self.initialized = False
        
        # 预设的回复模板
        self.response_templates = {
            "天气": [
                "今天天气不错，阳光明媚，温度适宜。",
                "今天可能会有小雨，记得带伞哦。",
                "今天天气晴朗，适合外出活动。"
            ],
            "音乐": [
                "好的，我为您播放一首轻松的音乐。",
                "正在为您搜索相关音乐，请稍等。",
                "我推荐您听一些古典音乐，很放松的。"
            ],
            "新闻": [
                "今天的主要新闻包括科技、财经和社会新闻。",
                "最新消息：科技领域有重要突破。",
                "今日头条新闻已为您整理好了。"
            ],
            "闹钟": [
                "好的，我已经为您设置了闹钟。",
                "闹钟设置成功，到时间我会提醒您。",
                "已添加到您的提醒列表中。"
            ],
            "默认": [
                "我理解您的问题，让我为您提供帮助。",
                "这是一个很好的问题，我来为您解答。",
                "感谢您的提问，我会尽力帮助您。",
                "我明白了，让我想想如何回答您。",
                "这个问题很有趣，我来分析一下。"
            ]
        }
    
    async def initialize(self) -> bool:
        """
        初始化LLM模型
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.bind(tag=TAG).info("开始初始化模拟LLM...")
            
            # 模拟初始化时间
            await asyncio.sleep(0.3)
            
            self.initialized = True
            self.logger.bind(tag=TAG).info("模拟LLM初始化完成")
            return True
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"模拟LLM初始化失败: {e}")
            return False
    
    async def response(
        self, 
        session_id: str, 
        dialogue: List[Dict[str, str]], 
        **kwargs
    ) -> Generator[str, None, None]:
        """
        生成LLM响应（流式）
        
        Args:
            session_id: 会话ID
            dialogue: 对话历史
            **kwargs: 其他参数
            
        Yields:
            str: 响应文本片段
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # 获取最后一条用户消息
            user_message = ""
            for msg in reversed(dialogue):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break
            
            # 根据关键词选择回复模板
            response_text = self._generate_response(user_message)
            
            self.logger.bind(tag=TAG).info(
                f"LLM生成响应 - 用户输入: {user_message[:50]}..., "
                f"响应长度: {len(response_text)}"
            )
            
            # 模拟流式输出
            words = response_text.split()
            for i, word in enumerate(words):
                # 模拟网络延迟
                await asyncio.sleep(0.05)
                
                if i == 0:
                    yield word
                else:
                    yield " " + word
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM响应生成失败: {e}")
            yield "抱歉，我现在无法回答您的问题。"
    
    def _generate_response(self, user_input: str) -> str:
        """
        根据用户输入生成回复
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            str: 生成的回复文本
        """
        # 简单的关键词匹配
        user_input_lower = user_input.lower()
        
        if any(keyword in user_input_lower for keyword in ["天气", "气温", "下雨", "晴天"]):
            category = "天气"
        elif any(keyword in user_input_lower for keyword in ["音乐", "歌曲", "播放"]):
            category = "音乐"
        elif any(keyword in user_input_lower for keyword in ["新闻", "消息", "头条"]):
            category = "新闻"
        elif any(keyword in user_input_lower for keyword in ["闹钟", "提醒", "设置"]):
            category = "闹钟"
        else:
            category = "默认"
        
        # 随机选择一个回复
        responses = self.response_templates.get(category, self.response_templates["默认"])
        base_response = random.choice(responses)
        
        # 添加一些个性化内容
        if category == "默认":
            base_response += f"您提到了：{user_input[:20]}..."
        
        return base_response
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.initialized = False
        await super().cleanup()
