"""
ASR (Automatic Speech Recognition) Provider基类
语音识别的抽象接口
"""
import os
import uuid
import wave
from abc import ABC, abstractmethod
from typing import Optional, Tuple, List, Dict, Any
from config.logger import setup_logging

TAG = __name__


class ASRProviderBase(ABC):
    """
    ASR Provider抽象基类
    定义语音识别的统一接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化ASR Provider
        
        Args:
            config: ASR配置字典
        """
        self.config = config
        self.logger = setup_logging()
        self.output_dir = config.get("output_dir", "tmp")
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.logger.bind(tag=TAG).info(f"ASR Provider初始化: {self.__class__.__name__}")
    
    @abstractmethod
    async def speech_to_text(
        self, 
        audio_data: List[bytes], 
        session_id: str, 
        audio_format: str = "opus"
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        将语音数据转换为文本
        
        Args:
            audio_data: 音频数据列表
            session_id: 会话ID
            audio_format: 音频格式（opus, wav等）
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (识别文本, 原始响应)
        """
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化ASR模型/服务
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.logger.bind(tag=TAG).info(f"ASR Provider清理资源: {self.__class__.__name__}")
    
    def save_audio_to_file(self, pcm_data: List[bytes], session_id: str) -> str:
        """
        将PCM数据保存为WAV文件
        
        Args:
            pcm_data: PCM音频数据列表
            session_id: 会话ID
            
        Returns:
            str: 保存的文件路径
        """
        module_name = self.__class__.__name__
        file_name = f"asr_{module_name}_{session_id}_{uuid.uuid4().hex[:8]}.wav"
        file_path = os.path.join(self.output_dir, file_name)
        
        try:
            with wave.open(file_path, "wb") as wf:
                wf.setnchannels(1)  # 单声道
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(16000)  # 16kHz采样率
                wf.writeframes(b"".join(pcm_data))
            
            self.logger.bind(tag=TAG).debug(f"音频文件已保存: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"保存音频文件失败: {e}")
            return ""
    
    @staticmethod
    def decode_opus(opus_data: List[bytes]) -> List[bytes]:
        """
        将Opus音频数据解码为PCM数据
        
        Args:
            opus_data: Opus音频数据列表
            
        Returns:
            List[bytes]: PCM音频数据列表
        """
        try:
            # 这里需要安装opuslib_next库
            # pip install opuslib_next
            import opuslib_next
            
            decoder = opuslib_next.Decoder(16000, 1)  # 16kHz, 单声道
            pcm_data = []
            buffer_size = 960  # 每次处理960个采样点
            
            for opus_packet in opus_data:
                try:
                    pcm_frame = decoder.decode(opus_packet, buffer_size)
                    if pcm_frame:
                        pcm_data.append(pcm_frame)
                except opuslib_next.OpusError as e:
                    # 跳过损坏的数据包
                    continue
                except Exception as e:
                    continue
            
            return pcm_data
            
        except ImportError:
            # 如果没有安装opuslib_next，返回原始数据
            return opus_data
        except Exception as e:
            return []
    
    def get_config(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        return self.config.get(key, default_value)
