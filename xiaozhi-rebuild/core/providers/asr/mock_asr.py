"""
模拟ASR实现
用于测试和演示ASR Provider的基本功能
"""
import asyncio
import random
from typing import List, Tuple, Optional, Dict, Any
from .base import ASRProviderBase

TAG = __name__


class MockASR(ASRProviderBase):
    """
    模拟ASR实现
    返回预设的识别结果，用于测试
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模拟ASR
        
        Args:
            config: ASR配置字典
        """
        super().__init__(config)
        self.initialized = False
        
        # 预设的识别结果
        self.mock_responses = [
            "你好，我想了解一下天气情况",
            "请帮我播放一首音乐",
            "今天的新闻有什么",
            "设置一个闹钟",
            "查询一下股票价格",
            "我想听个笑话",
            "帮我计算一下数学题",
            "查询附近的餐厅",
            "播放轻音乐",
            "关闭所有灯光"
        ]
    
    async def initialize(self) -> bool:
        """
        初始化ASR模型
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.bind(tag=TAG).info("开始初始化模拟ASR...")
            
            # 模拟初始化时间
            await asyncio.sleep(0.2)
            
            self.initialized = True
            self.logger.bind(tag=TAG).info("模拟ASR初始化完成")
            return True
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"模拟ASR初始化失败: {e}")
            return False
    
    async def speech_to_text(
        self, 
        audio_data: List[bytes], 
        session_id: str, 
        audio_format: str = "opus"
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        将语音数据转换为文本
        
        Args:
            audio_data: 音频数据列表
            session_id: 会话ID
            audio_format: 音频格式
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (识别文本, 原始响应)
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # 模拟ASR处理时间
            await asyncio.sleep(0.5)
            
            # 计算音频总长度
            total_length = sum(len(chunk) for chunk in audio_data)
            
            if total_length < 1000:  # 音频太短
                self.logger.bind(tag=TAG).warning("音频数据太短，无法识别")
                return None, None
            
            # 随机选择一个预设响应
            recognized_text = random.choice(self.mock_responses)
            
            # 模拟原始响应
            raw_response = {
                "text": recognized_text,
                "confidence": round(random.uniform(0.8, 0.99), 2),
                "audio_length": total_length,
                "session_id": session_id,
                "format": audio_format
            }
            
            self.logger.bind(tag=TAG).info(
                f"ASR识别完成 - 文本: {recognized_text}, "
                f"置信度: {raw_response['confidence']}, "
                f"音频长度: {total_length} bytes"
            )
            
            # 可选：保存音频文件用于调试
            if self.get_config("save_audio", False):
                if audio_format == "opus":
                    pcm_data = self.decode_opus(audio_data)
                    if pcm_data:
                        audio_file = self.save_audio_to_file(pcm_data, session_id)
                        self.logger.bind(tag=TAG).debug(f"音频文件已保存: {audio_file}")
            
            return recognized_text, str(raw_response)
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"ASR识别失败: {e}")
            return None, None
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        self.initialized = False
        await super().cleanup()
