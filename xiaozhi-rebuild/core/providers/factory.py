"""
Provider工厂模块
负责根据配置创建和管理各种AI Provider实例
"""
from typing import Dict, Any, Optional, Type
from config.logger import setup_logging

# 导入基类
from .vad.base import VADProviderBase
from .asr.base import ASRProviderBase
from .llm.base import LLMProviderBase
from .tts.base import TTSProviderBase

# 导入模拟实现
from .vad.mock_vad import MockVAD
from .asr.mock_asr import MockASR
from .llm.mock_llm import MockLLM
from .tts.mock_tts import MockTTS

TAG = __name__


class ProviderFactory:
    """
    Provider工厂类
    根据配置创建相应的Provider实例
    """
    
    # Provider注册表
    VAD_PROVIDERS: Dict[str, Type[VADProviderBase]] = {
        "MockVAD": MockVAD,
        # 可以在这里添加更多VAD实现
        # "SileroVAD": SileroVAD,
        # "WebRTCVAD": WebRTCVAD,
    }
    
    ASR_PROVIDERS: Dict[str, Type[ASRProviderBase]] = {
        "MockASR": MockASR,
        # 可以在这里添加更多ASR实现
        # "AliyunASR": AliyunASR,
        # "BaiduASR": BaiduASR,
    }
    
    LLM_PROVIDERS: Dict[str, Type[LLMProviderBase]] = {
        "MockLLM": MockLLM,
        # 可以在这里添加更多LLM实现
        # "OpenAILLM": OpenAILLM,
        # "GeminiLLM": GeminiLLM,
    }
    
    TTS_PROVIDERS: Dict[str, Type[TTSProviderBase]] = {
        "MockTTS": MockTTS,
        # 可以在这里添加更多TTS实现
        # "EdgeTTS": EdgeTTS,
        # "AliyunTTS": AliyunTTS,
    }
    
    def __init__(self):
        self.logger = setup_logging()
    
    def create_vad_provider(self, provider_name: str, config: Dict[str, Any]) -> Optional[VADProviderBase]:
        """
        创建VAD Provider实例
        
        Args:
            provider_name: Provider名称
            config: 配置字典
            
        Returns:
            Optional[VADProviderBase]: VAD Provider实例，失败返回None
        """
        try:
            if provider_name not in self.VAD_PROVIDERS:
                self.logger.bind(tag=TAG).error(f"未知的VAD Provider: {provider_name}")
                return None
            
            provider_class = self.VAD_PROVIDERS[provider_name]
            provider_instance = provider_class(config)
            
            self.logger.bind(tag=TAG).info(f"VAD Provider创建成功: {provider_name}")
            return provider_instance
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"创建VAD Provider失败: {provider_name}, 错误: {e}")
            return None
    
    def create_asr_provider(self, provider_name: str, config: Dict[str, Any]) -> Optional[ASRProviderBase]:
        """
        创建ASR Provider实例
        
        Args:
            provider_name: Provider名称
            config: 配置字典
            
        Returns:
            Optional[ASRProviderBase]: ASR Provider实例，失败返回None
        """
        try:
            if provider_name not in self.ASR_PROVIDERS:
                self.logger.bind(tag=TAG).error(f"未知的ASR Provider: {provider_name}")
                return None
            
            provider_class = self.ASR_PROVIDERS[provider_name]
            provider_instance = provider_class(config)
            
            self.logger.bind(tag=TAG).info(f"ASR Provider创建成功: {provider_name}")
            return provider_instance
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"创建ASR Provider失败: {provider_name}, 错误: {e}")
            return None
    
    def create_llm_provider(self, provider_name: str, config: Dict[str, Any]) -> Optional[LLMProviderBase]:
        """
        创建LLM Provider实例
        
        Args:
            provider_name: Provider名称
            config: 配置字典
            
        Returns:
            Optional[LLMProviderBase]: LLM Provider实例，失败返回None
        """
        try:
            if provider_name not in self.LLM_PROVIDERS:
                self.logger.bind(tag=TAG).error(f"未知的LLM Provider: {provider_name}")
                return None
            
            provider_class = self.LLM_PROVIDERS[provider_name]
            provider_instance = provider_class(config)
            
            self.logger.bind(tag=TAG).info(f"LLM Provider创建成功: {provider_name}")
            return provider_instance
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"创建LLM Provider失败: {provider_name}, 错误: {e}")
            return None
    
    def create_tts_provider(self, provider_name: str, config: Dict[str, Any]) -> Optional[TTSProviderBase]:
        """
        创建TTS Provider实例
        
        Args:
            provider_name: Provider名称
            config: 配置字典
            
        Returns:
            Optional[TTSProviderBase]: TTS Provider实例，失败返回None
        """
        try:
            if provider_name not in self.TTS_PROVIDERS:
                self.logger.bind(tag=TAG).error(f"未知的TTS Provider: {provider_name}")
                return None
            
            provider_class = self.TTS_PROVIDERS[provider_name]
            provider_instance = provider_class(config)
            
            self.logger.bind(tag=TAG).info(f"TTS Provider创建成功: {provider_name}")
            return provider_instance
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"创建TTS Provider失败: {provider_name}, 错误: {e}")
            return None
    
    def get_available_providers(self) -> Dict[str, list]:
        """
        获取所有可用的Provider列表
        
        Returns:
            Dict[str, list]: 各类型Provider的可用列表
        """
        return {
            "VAD": list(self.VAD_PROVIDERS.keys()),
            "ASR": list(self.ASR_PROVIDERS.keys()),
            "LLM": list(self.LLM_PROVIDERS.keys()),
            "TTS": list(self.TTS_PROVIDERS.keys()),
        }


class ProviderManager:
    """
    Provider管理器
    负责管理所有AI Provider的生命周期
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Provider管理器
        
        Args:
            config: 全局配置字典
        """
        self.config = config
        self.logger = setup_logging()
        self.factory = ProviderFactory()
        
        # Provider实例
        self.vad_provider: Optional[VADProviderBase] = None
        self.asr_provider: Optional[ASRProviderBase] = None
        self.llm_provider: Optional[LLMProviderBase] = None
        self.tts_provider: Optional[TTSProviderBase] = None
        
        self.logger.bind(tag=TAG).info("Provider管理器初始化完成")
    
    async def initialize_providers(self) -> bool:
        """
        根据配置初始化所有Provider
        
        Returns:
            bool: 是否全部初始化成功
        """
        try:
            selected_modules = self.config.get("selected_module", {})
            
            # 初始化VAD Provider
            vad_name = selected_modules.get("VAD", "MockVAD")
            vad_config = self.config.get("VAD", {}).get(vad_name, {})
            self.vad_provider = self.factory.create_vad_provider(vad_name, vad_config)
            
            # 初始化ASR Provider
            asr_name = selected_modules.get("ASR", "MockASR")
            asr_config = self.config.get("ASR", {}).get(asr_name, {})
            self.asr_provider = self.factory.create_asr_provider(asr_name, asr_config)
            
            # 初始化LLM Provider
            llm_name = selected_modules.get("LLM", "MockLLM")
            llm_config = self.config.get("LLM", {}).get(llm_name, {})
            self.llm_provider = self.factory.create_llm_provider(llm_name, llm_config)
            
            # 初始化TTS Provider
            tts_name = selected_modules.get("TTS", "MockTTS")
            tts_config = self.config.get("TTS", {}).get(tts_name, {})
            self.tts_provider = self.factory.create_tts_provider(tts_name, tts_config)
            
            # 检查是否所有Provider都创建成功
            if not all([self.vad_provider, self.asr_provider, self.llm_provider, self.tts_provider]):
                self.logger.bind(tag=TAG).error("部分Provider创建失败")
                return False
            
            # 初始化所有Provider
            init_results = await asyncio.gather(
                self.vad_provider.initialize(),
                self.asr_provider.initialize(),
                self.llm_provider.initialize(),
                self.tts_provider.initialize(),
                return_exceptions=True
            )
            
            # 检查初始化结果
            success_count = sum(1 for result in init_results if result is True)
            
            if success_count == 4:
                self.logger.bind(tag=TAG).info("所有AI Provider初始化成功")
                return True
            else:
                self.logger.bind(tag=TAG).error(f"Provider初始化失败，成功: {success_count}/4")
                return False
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Provider初始化异常: {e}")
            return False
    
    async def cleanup_providers(self) -> None:
        """
        清理所有Provider资源
        """
        try:
            cleanup_tasks = []
            
            if self.vad_provider:
                cleanup_tasks.append(self.vad_provider.cleanup())
            if self.asr_provider:
                cleanup_tasks.append(self.asr_provider.cleanup())
            if self.llm_provider:
                cleanup_tasks.append(self.llm_provider.cleanup())
            if self.tts_provider:
                cleanup_tasks.append(self.tts_provider.cleanup())
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self.logger.bind(tag=TAG).info("所有Provider资源清理完成")
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Provider资源清理异常: {e}")


# 导入asyncio用于cleanup_providers方法
import asyncio
