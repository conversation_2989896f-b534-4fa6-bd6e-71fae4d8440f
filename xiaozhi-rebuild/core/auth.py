"""
认证中间件模块
负责WebSocket连接的身份验证
"""
from typing import Dict, Set, Any
from config.logger import setup_logging

TAG = __name__


class AuthenticationError(Exception):
    """认证异常"""
    pass


class AuthMiddleware:
    """
    认证中间件
    支持Token认证和设备白名单
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化认证中间件
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = setup_logging(config)
        self.auth_config = config.get("server", {}).get("auth", {})
        
        # 构建token查找表
        self.tokens: Dict[str, str] = {
            item["token"]: item["name"]
            for item in self.auth_config.get("tokens", [])
        }
        
        # 设备白名单
        self.allowed_devices: Set[str] = set(
            self.auth_config.get("allowed_devices", [])
        )
        
        self.logger.bind(tag=TAG).info(
            f"认证中间件初始化完成 - 启用: {self.auth_config.get('enabled', False)}, "
            f"Token数量: {len(self.tokens)}, 白名单设备: {len(self.allowed_devices)}"
        )
    
    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """
        验证连接请求
        
        Args:
            headers: 请求头字典
            
        Returns:
            bool: 认证是否成功
            
        Raises:
            AuthenticationError: 认证失败
        """
        # 检查是否启用认证
        if not self.auth_config.get("enabled", False):
            self.logger.bind(tag=TAG).debug("认证未启用，跳过验证")
            return True
        
        # 获取设备ID
        device_id = headers.get("device-id", "")
        if not device_id:
            self.logger.bind(tag=TAG).error("缺少device-id")
            raise AuthenticationError("Missing device-id")
        
        # 检查设备是否在白名单中
        if self.allowed_devices and device_id in self.allowed_devices:
            self.logger.bind(tag=TAG).info(f"设备在白名单中，跳过Token验证: {device_id}")
            return True
        
        # 验证Authorization header
        auth_header = headers.get("authorization", "")
        if not auth_header:
            self.logger.bind(tag=TAG).error(f"缺少Authorization header - Device: {device_id}")
            raise AuthenticationError("Missing Authorization header")
        
        if not auth_header.startswith("Bearer "):
            self.logger.bind(tag=TAG).error(f"无效的Authorization格式 - Device: {device_id}")
            raise AuthenticationError("Invalid Authorization format")
        
        # 提取token
        try:
            token = auth_header.split(" ")[1]
        except IndexError:
            self.logger.bind(tag=TAG).error(f"无法提取token - Device: {device_id}")
            raise AuthenticationError("Invalid token format")
        
        # 验证token
        if token not in self.tokens:
            self.logger.bind(tag=TAG).error(f"无效的token: {token} - Device: {device_id}")
            raise AuthenticationError("Invalid token")
        
        device_name = self.tokens[token]
        self.logger.bind(tag=TAG).info(
            f"认证成功 - Device: {device_id}, Name: {device_name}"
        )
        return True
    
    def get_token_name(self, token: str) -> str:
        """
        获取token对应的设备名称
        
        Args:
            token: 认证token
            
        Returns:
            str: 设备名称，如果token不存在返回空字符串
        """
        return self.tokens.get(token, "")
    
    def is_device_allowed(self, device_id: str) -> bool:
        """
        检查设备是否在白名单中
        
        Args:
            device_id: 设备ID
            
        Returns:
            bool: 是否在白名单中
        """
        return device_id in self.allowed_devices
    
    def add_token(self, token: str, name: str) -> None:
        """
        动态添加token（运行时使用）
        
        Args:
            token: 认证token
            name: 设备名称
        """
        self.tokens[token] = name
        self.logger.bind(tag=TAG).info(f"添加新token: {name}")
    
    def remove_token(self, token: str) -> bool:
        """
        动态移除token（运行时使用）
        
        Args:
            token: 认证token
            
        Returns:
            bool: 是否成功移除
        """
        if token in self.tokens:
            name = self.tokens.pop(token)
            self.logger.bind(tag=TAG).info(f"移除token: {name}")
            return True
        return False
