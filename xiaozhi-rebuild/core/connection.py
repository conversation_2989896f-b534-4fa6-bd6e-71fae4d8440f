"""
连接处理器模块
负责处理单个WebSocket连接的生命周期
"""
import os
import json
import uuid
import time
import asyncio
import threading
from typing import Dict, Any, Optional
from urllib.parse import parse_qs, urlparse

import websockets
from websockets.exceptions import ConnectionClosed

from config.logger import setup_logging
from core.auth import AuthMiddleware, AuthenticationError
from core.providers.factory import ProviderManager
from plugins_func.loader import PluginManager
from core.handle.function_handler import FunctionCallHandler

TAG = __name__


class ConnectionHandler:
    """
    连接处理器
    管理单个WebSocket连接的完整生命周期
    """
    
    def __init__(self, config: Dict[str, Any], server=None, provider_manager: Optional[ProviderManager] = None, plugin_manager: Optional[PluginManager] = None):
        """
        初始化连接处理器

        Args:
            config: 配置字典
            server: WebSocket服务器实例
            provider_manager: AI Provider管理器
            plugin_manager: 插件管理器
        """
        self.config = config.copy()  # 创建配置副本
        self.server = server
        self.session_id = str(uuid.uuid4())
        self.logger = setup_logging(config)

        # AI Provider管理器
        self.provider_manager = provider_manager

        # 插件管理器
        self.plugin_manager = plugin_manager

        # Function Call处理器
        self.function_handler = FunctionCallHandler(plugin_manager) if plugin_manager else None

        # 认证中间件
        self.auth = AuthMiddleware(config)
        
        # 连接相关
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.headers: Dict[str, str] = {}
        self.device_id: Optional[str] = None
        self.client_id: Optional[str] = None
        self.client_ip: Optional[str] = None
        
        # 状态管理
        self.connected = False
        self.stop_event = threading.Event()
        self.timeout_task: Optional[asyncio.Task] = None
        self.timeout_seconds = int(config.get("connection", {}).get("close_connection_no_voice_time", 120))
        
        # 消息统计
        self.message_count = 0
        self.connect_time = time.time()
        
        self.logger.bind(tag=TAG).info(f"连接处理器初始化完成 - Session: {self.session_id}")
    
    async def handle_connection(self, websocket: websockets.WebSocketServerProtocol) -> None:
        """
        处理WebSocket连接的主要方法
        
        Args:
            websocket: WebSocket连接对象
        """
        try:
            self.websocket = websocket
            self.client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
            
            # 解析请求头
            await self._parse_headers()
            
            # 身份认证
            await self._authenticate()
            
            # 连接建立成功
            self.connected = True
            self.logger.bind(tag=TAG).info(
                f"连接建立成功 - IP: {self.client_ip}, Device: {self.device_id}, Session: {self.session_id}"
            )
            
            # 发送欢迎消息
            await self._send_welcome_message()
            
            # 启动超时检查
            self.timeout_task = asyncio.create_task(self._check_timeout())
            
            # 异步初始化组件（模拟）
            asyncio.create_task(self._initialize_components())
            
            # 进入消息循环
            await self._message_loop()
            
        except AuthenticationError as e:
            self.logger.bind(tag=TAG).error(f"认证失败: {e}")
            await self._send_error_message("Authentication failed")
        except ConnectionClosed:
            self.logger.bind(tag=TAG).info("客户端主动断开连接")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"连接处理异常: {e}")
        finally:
            await self._cleanup()
    
    async def _parse_headers(self) -> None:
        """解析请求头"""
        if not self.websocket:
            raise ValueError("WebSocket连接未建立")
        
        # 获取请求头
        self.headers = dict(self.websocket.request.headers)
        
        # 尝试从请求头获取device-id
        self.device_id = self.headers.get("device-id")
        self.client_id = self.headers.get("client-id")
        
        # 如果请求头中没有，尝试从URL参数获取
        if not self.device_id:
            request_path = self.websocket.request.path
            if request_path:
                parsed_url = urlparse(request_path)
                query_params = parse_qs(parsed_url.query)
                
                if "device-id" in query_params:
                    self.device_id = query_params["device-id"][0]
                    self.headers["device-id"] = self.device_id
                
                if "client-id" in query_params:
                    self.client_id = query_params["client-id"][0]
                    self.headers["client-id"] = self.client_id
        
        # 如果仍然没有device-id，这可能是测试连接
        if not self.device_id:
            await self.websocket.send("服务器运行正常，如需测试连接请使用test_page.html")
            raise ConnectionClosed(None, None)
        
        self.logger.bind(tag=TAG).info(f"请求头解析完成 - Headers: {self.headers}")
    
    async def _authenticate(self) -> None:
        """身份认证"""
        await self.auth.authenticate(self.headers)
    
    async def _send_welcome_message(self) -> None:
        """发送欢迎消息"""
        welcome_msg = {
            "type": "hello",
            "version": 1,
            "transport": "websocket",
            "session_id": self.session_id,
            "server_time": time.time(),
            "audio_params": self.config.get("audio", {
                "format": "opus",
                "sample_rate": 16000,
                "channels": 1,
                "frame_duration": 60
            })
        }
        
        await self.websocket.send(json.dumps(welcome_msg))
        self.logger.bind(tag=TAG).info("欢迎消息已发送")
    
    async def _send_error_message(self, error: str) -> None:
        """发送错误消息"""
        if self.websocket:
            try:
                error_msg = {
                    "type": "error",
                    "message": error,
                    "session_id": self.session_id
                }
                await self.websocket.send(json.dumps(error_msg))
            except Exception:
                pass  # 忽略发送错误
    
    async def _initialize_components(self) -> None:
        """异步初始化组件（模拟）"""
        self.logger.bind(tag=TAG).info("开始初始化AI组件...")
        
        # 模拟组件初始化时间
        await asyncio.sleep(1)
        
        self.logger.bind(tag=TAG).info("AI组件初始化完成")
        
        # 发送初始化完成消息
        init_msg = {
            "type": "system",
            "status": "ready",
            "message": "AI组件初始化完成，可以开始对话",
            "session_id": self.session_id
        }
        
        if self.websocket and self.connected:
            await self.websocket.send(json.dumps(init_msg))
    
    async def _message_loop(self) -> None:
        """消息循环"""
        async for message in self.websocket:
            try:
                # 重置超时计时器
                await self._reset_timeout()
                
                # 处理消息
                await self._route_message(message)
                
                # 更新消息计数
                self.message_count += 1
                
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"消息处理异常: {e}")
    
    async def _route_message(self, message) -> None:
        """消息路由"""
        if isinstance(message, str):
            # 文本消息
            await self._handle_text_message(message)
        elif isinstance(message, bytes):
            # 音频数据
            await self._handle_audio_message(message)
        else:
            self.logger.bind(tag=TAG).warning(f"未知消息类型: {type(message)}")
    
    async def _handle_text_message(self, message: str) -> None:
        """处理文本消息"""
        try:
            # 尝试解析JSON
            data = json.loads(message)
            msg_type = data.get("type", "unknown")
            
            self.logger.bind(tag=TAG).info(f"收到文本消息 - Type: {msg_type}, Content: {message[:100]}...")
            
            # 根据消息类型处理
            if msg_type == "ping":
                await self._handle_ping(data)
            elif msg_type == "text":
                await self._handle_chat_message(data)
            else:
                self.logger.bind(tag=TAG).warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError:
            # 纯文本消息
            self.logger.bind(tag=TAG).info(f"收到纯文本消息: {message}")
            await self._handle_chat_text(message)
    
    async def _handle_audio_message(self, audio_data: bytes) -> None:
        """处理音频消息"""
        self.logger.bind(tag=TAG).debug(f"收到音频数据: {len(audio_data)} bytes")

        try:
            # 使用真实的AI处理管道
            if self.provider_manager:
                # VAD检测
                vad_provider = self.provider_manager.vad_provider
                if vad_provider:
                    has_voice = await vad_provider.is_vad(self, audio_data)

                    if has_voice:
                        self.logger.bind(tag=TAG).info("VAD检测到语音活动，准备进行ASR识别")

                        # 这里可以累积音频数据，等待语音结束后进行ASR
                        # 为了演示，我们直接处理单个音频片段
                        await self._process_audio_for_asr([audio_data])
                    else:
                        self.logger.bind(tag=TAG).debug("VAD未检测到语音活动")

            # 发送音频接收确认
            ack_msg = {
                "type": "audio_ack",
                "size": len(audio_data),
                "has_voice": has_voice if 'has_voice' in locals() else False,
                "session_id": self.session_id
            }
            await self.websocket.send(json.dumps(ack_msg))

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"音频处理异常: {e}")

            # 发送错误确认
            ack_msg = {
                "type": "audio_ack",
                "size": len(audio_data),
                "error": str(e),
                "session_id": self.session_id
            }
            await self.websocket.send(json.dumps(ack_msg))

    async def _process_audio_for_asr(self, audio_data_list: list) -> None:
        """
        处理音频进行ASR识别

        Args:
            audio_data_list: 音频数据列表
        """
        try:
            if not self.provider_manager or not self.provider_manager.asr_provider:
                self.logger.bind(tag=TAG).warning("ASR Provider未初始化")
                return

            # 进行ASR识别
            asr_provider = self.provider_manager.asr_provider
            recognized_text, raw_response = await asr_provider.speech_to_text(
                audio_data_list,
                self.session_id,
                "opus"
            )

            if recognized_text:
                self.logger.bind(tag=TAG).info(f"ASR识别结果: {recognized_text}")

                # 发送ASR结果
                asr_result_msg = {
                    "type": "asr_result",
                    "text": recognized_text,
                    "session_id": self.session_id,
                    "timestamp": time.time()
                }
                await self.websocket.send(json.dumps(asr_result_msg))

                # 进行LLM处理
                await self._process_text_with_llm(recognized_text)
            else:
                self.logger.bind(tag=TAG).warning("ASR识别失败或结果为空")

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"ASR处理异常: {e}")

    async def _process_text_with_llm(self, text: str) -> None:
        """
        使用LLM处理文本

        Args:
            text: 要处理的文本
        """
        try:
            if not self.provider_manager or not self.provider_manager.llm_provider:
                self.logger.bind(tag=TAG).warning("LLM Provider未初始化")
                return

            # 构建对话历史
            dialogue = [
                {"role": "system", "content": self.config.get("prompt", "你是一个友善的AI助手。")},
                {"role": "user", "content": text}
            ]

            # 获取LLM响应
            llm_provider = self.provider_manager.llm_provider
            response_text = ""

            # 发送开始响应消息
            start_msg = {
                "type": "llm_start",
                "session_id": self.session_id,
                "timestamp": time.time()
            }
            await self.websocket.send(json.dumps(start_msg))

            # 流式接收LLM响应
            async for token in llm_provider.response(self.session_id, dialogue):
                response_text += token

                # 发送流式响应
                stream_msg = {
                    "type": "llm_stream",
                    "token": token,
                    "session_id": self.session_id
                }
                await self.websocket.send(json.dumps(stream_msg))

            # 发送完整响应
            complete_msg = {
                "type": "llm_complete",
                "text": response_text,
                "session_id": self.session_id,
                "timestamp": time.time()
            }
            await self.websocket.send(json.dumps(complete_msg))

            self.logger.bind(tag=TAG).info(f"LLM响应完成: {response_text[:100]}...")

            # 进行TTS合成
            await self._process_text_with_tts(response_text)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM处理异常: {e}")

    async def _process_text_with_tts(self, text: str) -> None:
        """
        使用TTS合成语音

        Args:
            text: 要合成的文本
        """
        try:
            if not self.provider_manager or not self.provider_manager.tts_provider:
                self.logger.bind(tag=TAG).warning("TTS Provider未初始化")
                return

            # 进行TTS合成
            tts_provider = self.provider_manager.tts_provider
            audio_file = await tts_provider.text_to_speech(text, self.session_id)

            if audio_file and os.path.exists(audio_file):
                # 发送TTS结果
                tts_result_msg = {
                    "type": "tts_result",
                    "audio_file": os.path.basename(audio_file),
                    "text": text,
                    "session_id": self.session_id,
                    "timestamp": time.time()
                }
                await self.websocket.send(json.dumps(tts_result_msg))

                self.logger.bind(tag=TAG).info(f"TTS合成完成: {audio_file}")

                # 可选：发送音频数据流
                # await self._send_audio_stream(audio_file)
            else:
                self.logger.bind(tag=TAG).warning("TTS合成失败")

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"TTS处理异常: {e}")

    async def _handle_ping(self, data: Dict[str, Any]) -> None:
        """处理ping消息"""
        pong_msg = {
            "type": "pong",
            "timestamp": time.time(),
            "session_id": self.session_id
        }
        await self.websocket.send(json.dumps(pong_msg))
    
    async def _handle_chat_message(self, data: Dict[str, Any]) -> None:
        """处理聊天消息"""
        content = data.get("content", "")
        await self._handle_chat_text(content)
    
    async def _handle_chat_text(self, text: str) -> None:
        """处理聊天文本"""
        self.logger.bind(tag=TAG).info(f"处理聊天文本: {text}")

        try:
            # 首先尝试插件功能处理
            if self.function_handler:
                has_function, direct_response, function_results = await self.function_handler.handle_intent_and_functions(
                    text, self.session_id, self
                )

                if has_function and direct_response:
                    # 有插件处理且有直接响应，发送插件响应
                    response_msg = {
                        "type": "function_response",
                        "content": direct_response,
                        "function_results": function_results,
                        "session_id": self.session_id,
                        "timestamp": time.time()
                    }

                    await self.websocket.send(json.dumps(response_msg))

                    # 如果插件返回了直接响应，可以选择是否继续LLM处理
                    # 这里我们直接返回，不再调用LLM
                    return
                elif has_function:
                    # 有插件处理但无直接响应，将结果传递给LLM
                    function_context = self.function_handler.format_function_results_for_llm(function_results)
                    enhanced_text = f"{text}\n\n插件执行结果：\n{function_context}"
                    await self._process_text_with_llm(enhanced_text)
                    return

            # 没有插件处理或插件未启用，直接使用LLM处理
            await self._process_text_with_llm(text)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理聊天文本异常: {e}")

            # 发送错误响应
            error_msg = {
                "type": "error",
                "content": "处理消息时发生错误",
                "session_id": self.session_id,
                "timestamp": time.time()
            }

            await self.websocket.send(json.dumps(error_msg))
    
    async def _reset_timeout(self) -> None:
        """重置超时计时器"""
        if self.timeout_task:
            self.timeout_task.cancel()
        self.timeout_task = asyncio.create_task(self._check_timeout())
    
    async def _check_timeout(self) -> None:
        """检查连接超时"""
        try:
            await asyncio.sleep(self.timeout_seconds)
            if self.connected:
                self.logger.bind(tag=TAG).info("连接超时，准备关闭")
                await self._cleanup()
        except asyncio.CancelledError:
            # 超时任务被取消，正常情况
            pass
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"超时检查异常: {e}")
    
    async def _cleanup(self) -> None:
        """清理资源"""
        if not self.connected:
            return
        
        self.connected = False
        self.stop_event.set()
        
        # 取消超时任务
        if self.timeout_task:
            self.timeout_task.cancel()
            self.timeout_task = None
        
        # 关闭WebSocket连接
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"关闭WebSocket连接异常: {e}")
        
        # 计算连接时长
        connection_duration = time.time() - self.connect_time
        
        self.logger.bind(tag=TAG).info(
            f"连接已关闭 - Session: {self.session_id}, "
            f"持续时间: {connection_duration:.2f}s, 消息数: {self.message_count}"
        )
