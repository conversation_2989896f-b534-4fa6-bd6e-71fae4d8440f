"""
连接处理器模块
负责处理单个WebSocket连接的生命周期
"""
import json
import uuid
import time
import asyncio
import threading
from typing import Dict, Any, Optional
from urllib.parse import parse_qs, urlparse

import websockets
from websockets.exceptions import ConnectionClosed

from config.logger import setup_logging
from core.auth import AuthMiddleware, AuthenticationError

TAG = __name__


class ConnectionHandler:
    """
    连接处理器
    管理单个WebSocket连接的完整生命周期
    """
    
    def __init__(self, config: Dict[str, Any], server=None):
        """
        初始化连接处理器
        
        Args:
            config: 配置字典
            server: WebSocket服务器实例
        """
        self.config = config.copy()  # 创建配置副本
        self.server = server
        self.session_id = str(uuid.uuid4())
        self.logger = setup_logging(config)
        
        # 认证中间件
        self.auth = AuthMiddleware(config)
        
        # 连接相关
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.headers: Dict[str, str] = {}
        self.device_id: Optional[str] = None
        self.client_id: Optional[str] = None
        self.client_ip: Optional[str] = None
        
        # 状态管理
        self.connected = False
        self.stop_event = threading.Event()
        self.timeout_task: Optional[asyncio.Task] = None
        self.timeout_seconds = int(config.get("connection", {}).get("close_connection_no_voice_time", 120))
        
        # 消息统计
        self.message_count = 0
        self.connect_time = time.time()
        
        self.logger.bind(tag=TAG).info(f"连接处理器初始化完成 - Session: {self.session_id}")
    
    async def handle_connection(self, websocket: websockets.WebSocketServerProtocol) -> None:
        """
        处理WebSocket连接的主要方法
        
        Args:
            websocket: WebSocket连接对象
        """
        try:
            self.websocket = websocket
            self.client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
            
            # 解析请求头
            await self._parse_headers()
            
            # 身份认证
            await self._authenticate()
            
            # 连接建立成功
            self.connected = True
            self.logger.bind(tag=TAG).info(
                f"连接建立成功 - IP: {self.client_ip}, Device: {self.device_id}, Session: {self.session_id}"
            )
            
            # 发送欢迎消息
            await self._send_welcome_message()
            
            # 启动超时检查
            self.timeout_task = asyncio.create_task(self._check_timeout())
            
            # 异步初始化组件（模拟）
            asyncio.create_task(self._initialize_components())
            
            # 进入消息循环
            await self._message_loop()
            
        except AuthenticationError as e:
            self.logger.bind(tag=TAG).error(f"认证失败: {e}")
            await self._send_error_message("Authentication failed")
        except ConnectionClosed:
            self.logger.bind(tag=TAG).info("客户端主动断开连接")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"连接处理异常: {e}")
        finally:
            await self._cleanup()
    
    async def _parse_headers(self) -> None:
        """解析请求头"""
        if not self.websocket:
            raise ValueError("WebSocket连接未建立")
        
        # 获取请求头
        self.headers = dict(self.websocket.request.headers)
        
        # 尝试从请求头获取device-id
        self.device_id = self.headers.get("device-id")
        self.client_id = self.headers.get("client-id")
        
        # 如果请求头中没有，尝试从URL参数获取
        if not self.device_id:
            request_path = self.websocket.request.path
            if request_path:
                parsed_url = urlparse(request_path)
                query_params = parse_qs(parsed_url.query)
                
                if "device-id" in query_params:
                    self.device_id = query_params["device-id"][0]
                    self.headers["device-id"] = self.device_id
                
                if "client-id" in query_params:
                    self.client_id = query_params["client-id"][0]
                    self.headers["client-id"] = self.client_id
        
        # 如果仍然没有device-id，这可能是测试连接
        if not self.device_id:
            await self.websocket.send("服务器运行正常，如需测试连接请使用test_page.html")
            raise ConnectionClosed(None, None)
        
        self.logger.bind(tag=TAG).info(f"请求头解析完成 - Headers: {self.headers}")
    
    async def _authenticate(self) -> None:
        """身份认证"""
        await self.auth.authenticate(self.headers)
    
    async def _send_welcome_message(self) -> None:
        """发送欢迎消息"""
        welcome_msg = {
            "type": "hello",
            "version": 1,
            "transport": "websocket",
            "session_id": self.session_id,
            "server_time": time.time(),
            "audio_params": self.config.get("audio", {
                "format": "opus",
                "sample_rate": 16000,
                "channels": 1,
                "frame_duration": 60
            })
        }
        
        await self.websocket.send(json.dumps(welcome_msg))
        self.logger.bind(tag=TAG).info("欢迎消息已发送")
    
    async def _send_error_message(self, error: str) -> None:
        """发送错误消息"""
        if self.websocket:
            try:
                error_msg = {
                    "type": "error",
                    "message": error,
                    "session_id": self.session_id
                }
                await self.websocket.send(json.dumps(error_msg))
            except Exception:
                pass  # 忽略发送错误
    
    async def _initialize_components(self) -> None:
        """异步初始化组件（模拟）"""
        self.logger.bind(tag=TAG).info("开始初始化AI组件...")
        
        # 模拟组件初始化时间
        await asyncio.sleep(1)
        
        self.logger.bind(tag=TAG).info("AI组件初始化完成")
        
        # 发送初始化完成消息
        init_msg = {
            "type": "system",
            "status": "ready",
            "message": "AI组件初始化完成，可以开始对话",
            "session_id": self.session_id
        }
        
        if self.websocket and self.connected:
            await self.websocket.send(json.dumps(init_msg))
    
    async def _message_loop(self) -> None:
        """消息循环"""
        async for message in self.websocket:
            try:
                # 重置超时计时器
                await self._reset_timeout()
                
                # 处理消息
                await self._route_message(message)
                
                # 更新消息计数
                self.message_count += 1
                
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"消息处理异常: {e}")
    
    async def _route_message(self, message) -> None:
        """消息路由"""
        if isinstance(message, str):
            # 文本消息
            await self._handle_text_message(message)
        elif isinstance(message, bytes):
            # 音频数据
            await self._handle_audio_message(message)
        else:
            self.logger.bind(tag=TAG).warning(f"未知消息类型: {type(message)}")
    
    async def _handle_text_message(self, message: str) -> None:
        """处理文本消息"""
        try:
            # 尝试解析JSON
            data = json.loads(message)
            msg_type = data.get("type", "unknown")
            
            self.logger.bind(tag=TAG).info(f"收到文本消息 - Type: {msg_type}, Content: {message[:100]}...")
            
            # 根据消息类型处理
            if msg_type == "ping":
                await self._handle_ping(data)
            elif msg_type == "text":
                await self._handle_chat_message(data)
            else:
                self.logger.bind(tag=TAG).warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError:
            # 纯文本消息
            self.logger.bind(tag=TAG).info(f"收到纯文本消息: {message}")
            await self._handle_chat_text(message)
    
    async def _handle_audio_message(self, audio_data: bytes) -> None:
        """处理音频消息"""
        self.logger.bind(tag=TAG).debug(f"收到音频数据: {len(audio_data)} bytes")
        
        # 模拟音频处理
        # 在真实实现中，这里会进行VAD检测和ASR识别
        
        # 发送音频接收确认
        ack_msg = {
            "type": "audio_ack",
            "size": len(audio_data),
            "session_id": self.session_id
        }
        await self.websocket.send(json.dumps(ack_msg))
    
    async def _handle_ping(self, data: Dict[str, Any]) -> None:
        """处理ping消息"""
        pong_msg = {
            "type": "pong",
            "timestamp": time.time(),
            "session_id": self.session_id
        }
        await self.websocket.send(json.dumps(pong_msg))
    
    async def _handle_chat_message(self, data: Dict[str, Any]) -> None:
        """处理聊天消息"""
        content = data.get("content", "")
        await self._handle_chat_text(content)
    
    async def _handle_chat_text(self, text: str) -> None:
        """处理聊天文本"""
        self.logger.bind(tag=TAG).info(f"处理聊天文本: {text}")
        
        # 模拟AI回复
        response = f"收到您的消息：{text}。这是一个模拟回复。"
        
        response_msg = {
            "type": "response",
            "content": response,
            "session_id": self.session_id,
            "timestamp": time.time()
        }
        
        await self.websocket.send(json.dumps(response_msg))
    
    async def _reset_timeout(self) -> None:
        """重置超时计时器"""
        if self.timeout_task:
            self.timeout_task.cancel()
        self.timeout_task = asyncio.create_task(self._check_timeout())
    
    async def _check_timeout(self) -> None:
        """检查连接超时"""
        try:
            await asyncio.sleep(self.timeout_seconds)
            if self.connected:
                self.logger.bind(tag=TAG).info("连接超时，准备关闭")
                await self._cleanup()
        except asyncio.CancelledError:
            # 超时任务被取消，正常情况
            pass
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"超时检查异常: {e}")
    
    async def _cleanup(self) -> None:
        """清理资源"""
        if not self.connected:
            return
        
        self.connected = False
        self.stop_event.set()
        
        # 取消超时任务
        if self.timeout_task:
            self.timeout_task.cancel()
            self.timeout_task = None
        
        # 关闭WebSocket连接
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"关闭WebSocket连接异常: {e}")
        
        # 计算连接时长
        connection_duration = time.time() - self.connect_time
        
        self.logger.bind(tag=TAG).info(
            f"连接已关闭 - Session: {self.session_id}, "
            f"持续时间: {connection_duration:.2f}s, 消息数: {self.message_count}"
        )
