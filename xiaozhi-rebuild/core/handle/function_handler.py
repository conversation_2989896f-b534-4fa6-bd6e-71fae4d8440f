"""
Function Call处理器
负责处理LLM的函数调用请求
"""
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from config.logger import setup_logging
from plugins_func.loader import PluginManager
from plugins_func.register import ActionResponse, Action

TAG = __name__


class FunctionCallHandler:
    """Function Call处理器"""
    
    def __init__(self, plugin_manager: PluginManager):
        """
        初始化Function Call处理器
        
        Args:
            plugin_manager: 插件管理器
        """
        self.plugin_manager = plugin_manager
        self.logger = setup_logging()
        self.logger.bind(tag=TAG).info("Function Call处理器初始化完成")
    
    async def handle_function_call(
        self, 
        function_name: str, 
        function_args: Dict[str, Any],
        session_id: str,
        conn=None
    ) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """
        处理函数调用
        
        Args:
            function_name: 函数名称
            function_args: 函数参数
            session_id: 会话ID
            conn: 连接对象（某些函数需要）
            
        Returns:
            Tuple[bool, Optional[str], Optional[Dict[str, Any]]]: 
            (是否成功, 响应文本, 详细结果)
        """
        try:
            self.logger.bind(tag=TAG).info(
                f"处理函数调用 - 函数: {function_name}, "
                f"参数: {function_args}, 会话: {session_id}"
            )
            
            # 检查插件系统是否启用
            if not self.plugin_manager.enabled:
                self.logger.bind(tag=TAG).warning("插件系统未启用")
                return False, "插件系统未启用", None
            
            # 获取函数项
            func_item = self.plugin_manager.registry.get_function(function_name)
            if not func_item:
                self.logger.bind(tag=TAG).error(f"函数 '{function_name}' 未找到")
                return False, f"函数 '{function_name}' 未找到", None
            
            # 准备函数参数
            kwargs = function_args.copy()
            kwargs["session_id"] = session_id
            
            # 根据工具类型决定是否传递conn参数
            if func_item.tool_type.code in [4, 5]:  # SYSTEM_CTL, IOT_CTL
                kwargs["conn"] = conn
            
            # 执行函数
            try:
                # 检查函数是否是异步的
                if asyncio.iscoroutinefunction(func_item.func):
                    result = await func_item.func(**kwargs)
                else:
                    result = func_item.func(**kwargs)
                
                # 处理结果
                if isinstance(result, ActionResponse):
                    success = result.action != Action.ERROR
                    response_text = result.response
                    detailed_result = result.to_dict()
                    
                    if success:
                        self.logger.bind(tag=TAG).info(
                            f"函数 '{function_name}' 执行成功 - 动作: {result.action.name}"
                        )
                    else:
                        self.logger.bind(tag=TAG).error(
                            f"函数 '{function_name}' 执行失败 - 错误: {response_text}"
                        )
                    
                    return success, response_text, detailed_result
                else:
                    # 兼容性处理：如果返回的不是ActionResponse
                    self.logger.bind(tag=TAG).info(f"函数 '{function_name}' 执行完成")
                    return True, str(result), {"raw_result": result}
                    
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"函数 '{function_name}' 执行异常: {e}")
                return False, f"函数执行异常: {str(e)}", None
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理函数调用异常: {e}")
            return False, f"处理函数调用异常: {str(e)}", None
    
    async def parse_function_call_from_llm(
        self, 
        llm_response: str
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """
        从LLM响应中解析函数调用
        
        Args:
            llm_response: LLM响应文本
            
        Returns:
            List[Tuple[str, Dict[str, Any]]]: 函数调用列表 [(函数名, 参数)]
        """
        function_calls = []
        
        try:
            # 尝试解析JSON格式的函数调用
            # 这里简化处理，实际应该根据具体的LLM响应格式来解析
            
            # 查找可能的函数调用模式
            import re
            
            # 模式1: {"function": "function_name", "arguments": {...}}
            pattern1 = r'\{"function":\s*"([^"]+)",\s*"arguments":\s*(\{[^}]*\})\}'
            matches1 = re.findall(pattern1, llm_response)
            
            for func_name, args_str in matches1:
                try:
                    args = json.loads(args_str)
                    function_calls.append((func_name, args))
                except json.JSONDecodeError:
                    continue
            
            # 模式2: function_name(arg1=value1, arg2=value2)
            pattern2 = r'(\w+)\(([^)]*)\)'
            matches2 = re.findall(pattern2, llm_response)
            
            for func_name, args_str in matches2:
                # 简单解析参数（这里可以更复杂）
                args = {}
                if args_str.strip():
                    # 这里简化处理，实际需要更复杂的解析
                    pass
                
                # 只有在函数存在时才添加
                if self.plugin_manager.registry.get_function(func_name):
                    function_calls.append((func_name, args))
            
            self.logger.bind(tag=TAG).debug(f"解析到 {len(function_calls)} 个函数调用")
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"解析函数调用异常: {e}")
        
        return function_calls
    
    def get_available_functions_for_llm(self) -> List[Dict[str, Any]]:
        """
        获取可用于LLM的函数描述列表
        
        Returns:
            List[Dict[str, Any]]: 函数描述列表
        """
        return self.plugin_manager.get_function_descriptions_for_llm()
    
    async def execute_function_calls(
        self, 
        function_calls: List[Tuple[str, Dict[str, Any]]],
        session_id: str,
        conn=None
    ) -> List[Dict[str, Any]]:
        """
        批量执行函数调用
        
        Args:
            function_calls: 函数调用列表
            session_id: 会话ID
            conn: 连接对象
            
        Returns:
            List[Dict[str, Any]]: 执行结果列表
        """
        results = []
        
        for func_name, func_args in function_calls:
            success, response_text, detailed_result = await self.handle_function_call(
                func_name, func_args, session_id, conn
            )
            
            result_item = {
                "function_name": func_name,
                "function_args": func_args,
                "success": success,
                "response_text": response_text,
                "detailed_result": detailed_result,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            results.append(result_item)
        
        return results
    
    def format_function_results_for_llm(
        self, 
        results: List[Dict[str, Any]]
    ) -> str:
        """
        格式化函数执行结果供LLM使用
        
        Args:
            results: 函数执行结果列表
            
        Returns:
            str: 格式化后的结果文本
        """
        if not results:
            return ""
        
        formatted_parts = []
        
        for result in results:
            func_name = result["function_name"]
            success = result["success"]
            response_text = result["response_text"]
            
            if success:
                formatted_parts.append(f"函数 {func_name} 执行成功：{response_text}")
            else:
                formatted_parts.append(f"函数 {func_name} 执行失败：{response_text}")
        
        return "\n".join(formatted_parts)
    
    async def handle_intent_and_functions(
        self, 
        user_input: str, 
        session_id: str,
        conn=None
    ) -> Tuple[bool, Optional[str], List[Dict[str, Any]]]:
        """
        处理用户输入的意图识别和函数调用
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            conn: 连接对象
            
        Returns:
            Tuple[bool, Optional[str], List[Dict[str, Any]]]: 
            (是否有函数调用, 直接响应文本, 函数执行结果)
        """
        try:
            # 简单的意图识别（基于关键词）
            function_calls = await self.detect_intent_and_extract_functions(user_input)
            
            if not function_calls:
                return False, None, []
            
            # 执行函数调用
            results = await self.execute_function_calls(function_calls, session_id, conn)
            
            # 检查是否有直接响应
            direct_responses = []
            for result in results:
                if result["success"] and result["response_text"]:
                    direct_responses.append(result["response_text"])
            
            if direct_responses:
                combined_response = "\n".join(direct_responses)
                return True, combined_response, results
            else:
                return True, None, results
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理意图和函数调用异常: {e}")
            return False, None, []
    
    async def detect_intent_and_extract_functions(
        self, 
        user_input: str
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """
        检测意图并提取函数调用
        
        Args:
            user_input: 用户输入
            
        Returns:
            List[Tuple[str, Dict[str, Any]]]: 函数调用列表
        """
        function_calls = []
        user_input_lower = user_input.lower()
        
        # 时间相关意图
        if any(keyword in user_input_lower for keyword in ["时间", "几点", "现在", "日期", "今天"]):
            if "日期" in user_input_lower or "今天" in user_input_lower:
                function_calls.append(("get_date", {"include_weekday": True}))
            else:
                function_calls.append(("get_time", {"format": "datetime"}))
        
        # 天气相关意图
        if any(keyword in user_input_lower for keyword in ["天气", "气温", "下雨", "晴天", "阴天"]):
            # 尝试提取城市名称
            import re
            city_pattern = r'([北上广深杭南武成重西天青大厦苏无]\w*[市]?)'
            city_match = re.search(city_pattern, user_input)
            city = city_match.group(1) if city_match else "北京"
            
            # 检查是否询问多天天气
            if any(keyword in user_input_lower for keyword in ["几天", "未来", "明天", "后天"]):
                days = 3
            else:
                days = 1
            
            function_calls.append(("get_weather", {"city": city, "days": days}))
        
        # 计算相关意图
        if any(keyword in user_input_lower for keyword in ["计算", "算", "+", "-", "*", "/", "等于"]):
            # 尝试提取数学表达式
            import re
            # 简单的数学表达式匹配
            math_pattern = r'[\d+\-*/().\s]+'
            math_match = re.search(math_pattern, user_input)
            if math_match:
                expression = math_match.group().strip()
                if expression:
                    function_calls.append(("calculate", {"expression": expression}))
        
        # 单位转换意图
        if any(keyword in user_input_lower for keyword in ["转换", "换算", "等于多少"]):
            # 这里可以添加更复杂的单位转换解析逻辑
            pass
        
        return function_calls
