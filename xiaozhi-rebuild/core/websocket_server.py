"""
WebSocket服务器模块
负责管理WebSocket连接和AI模块
"""
import asyncio
from typing import Dict, Any, Set
import websockets
from websockets.exceptions import ConnectionClosed

from config.logger import setup_logging
from core.connection import ConnectionHandler
from core.providers.factory import ProviderManager
from plugins_func.loader import PluginManager

TAG = __name__


class WebSocketServer:
    """
    WebSocket服务器
    管理所有WebSocket连接和AI模块
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化WebSocket服务器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = setup_logging(config)
        
        # 连接管理
        self.active_connections: Set[ConnectionHandler] = set()
        self.config_lock = asyncio.Lock()
        self.server = None  # WebSocket服务器实例

        # 服务器配置
        self.server_config = config.get("server", {})
        self.host = self.server_config.get("ip", "0.0.0.0")
        self.port = int(self.server_config.get("port", 8000))
        
        # AI模块管理器（第三阶段实现）
        self.provider_manager = ProviderManager(config)

        # 插件管理器（第四阶段实现）
        self.plugin_manager = PluginManager(config)
        
        self.logger.bind(tag=TAG).info(
            f"WebSocket服务器初始化完成 - 监听地址: {self.host}:{self.port}"
        )
    
    async def start(self) -> None:
        """
        启动WebSocket服务器
        """
        self.logger.bind(tag=TAG).info(f"启动WebSocket服务器在 {self.host}:{self.port}")

        # 初始化AI模块（第二阶段模拟）
        await self._initialize_ai_modules()

        # 启动WebSocket服务
        try:
            self.server = await websockets.serve(
                self._handle_connection,
                self.host,
                self.port,
                process_request=self._process_request
            )

            self.logger.bind(tag=TAG).info("WebSocket服务器启动成功")

            # 保持服务器运行
            await self.server.wait_closed()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"WebSocket服务器启动失败: {e}")
            raise
    
    async def _initialize_ai_modules(self) -> None:
        """
        初始化AI模块和插件系统（第四阶段完整实现）
        """
        self.logger.bind(tag=TAG).info("开始初始化AI模块和插件系统...")

        # 初始化AI Provider
        provider_success = await self.provider_manager.initialize_providers()

        # 初始化插件系统
        plugin_success = await self.plugin_manager.initialize()

        if provider_success and plugin_success:
            self.logger.bind(tag=TAG).info("AI模块和插件系统初始化完成")
        else:
            error_msg = []
            if not provider_success:
                error_msg.append("AI模块初始化失败")
            if not plugin_success:
                error_msg.append("插件系统初始化失败")

            self.logger.bind(tag=TAG).error("; ".join(error_msg))
            raise RuntimeError("; ".join(error_msg))
    
    async def _handle_connection(self, websocket: websockets.WebSocketServerProtocol) -> None:
        """
        处理新的WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
        """
        # 创建连接处理器，传入AI Provider和插件管理器
        handler = ConnectionHandler(
            self.config,
            server=self,
            provider_manager=self.provider_manager,
            plugin_manager=self.plugin_manager
        )
        
        # 添加到活跃连接集合
        self.active_connections.add(handler)
        
        try:
            # 处理连接
            await handler.handle_connection(websocket)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"连接处理异常: {e}")
        finally:
            # 从活跃连接中移除
            self.active_connections.discard(handler)
            
            # 记录连接统计
            self.logger.bind(tag=TAG).info(
                f"连接已移除 - 当前活跃连接数: {len(self.active_connections)}"
            )
    
    async def _process_request(self, websocket, request_headers) -> None:
        """
        处理HTTP请求（非WebSocket升级请求）
        
        Args:
            websocket: WebSocket对象
            request_headers: 请求头
        """
        # 检查是否为WebSocket升级请求
        connection_header = request_headers.headers.get("connection", "").lower()
        if "upgrade" in connection_header:
            # WebSocket升级请求，返回None允许继续
            return None
        else:
            # 普通HTTP请求，返回状态信息
            status_info = {
                "status": "running",
                "active_connections": len(self.active_connections),
                "server_version": "1.0.0-rebuild",
                "message": "xiaozhi-server WebSocket服务运行中"
            }
            
            import json
            response_body = json.dumps(status_info, ensure_ascii=False, indent=2)
            
            return websocket.respond(
                200,
                response_body,
                headers=[("Content-Type", "application/json; charset=utf-8")]
            )
    
    async def update_config(self) -> bool:
        """
        更新服务器配置并重新初始化组件
        
        Returns:
            bool: 更新是否成功
        """
        try:
            async with self.config_lock:
                self.logger.bind(tag=TAG).info("开始更新服务器配置...")
                
                # 在真实实现中，这里会从API获取新配置
                # new_config = get_config_from_api(self.config)
                
                # 模拟配置更新
                await asyncio.sleep(0.1)
                
                # 重新初始化AI模块
                await self._initialize_ai_modules()
                
                self.logger.bind(tag=TAG).info("服务器配置更新完成")
                return True
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"更新服务器配置失败: {e}")
            return False
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息
        
        Returns:
            Dict[str, Any]: 连接统计信息
        """
        return {
            "active_connections": len(self.active_connections),
            "server_host": self.host,
            "server_port": self.port,
            "ai_modules": {
                "vad": self._vad is not None,
                "asr": self._asr is not None,
                "llm": self._llm is not None,
                "memory": self._memory is not None,
                "intent": self._intent is not None,
            }
        }
    
    async def broadcast_message(self, message: Dict[str, Any]) -> int:
        """
        向所有活跃连接广播消息
        
        Args:
            message: 要广播的消息
            
        Returns:
            int: 成功发送的连接数
        """
        if not self.active_connections:
            return 0
        
        import json
        message_str = json.dumps(message)
        success_count = 0
        
        # 创建发送任务列表
        send_tasks = []
        for handler in self.active_connections.copy():  # 使用副本避免并发修改
            if handler.connected and handler.websocket:
                send_tasks.append(self._send_to_connection(handler, message_str))
        
        # 并发发送
        if send_tasks:
            results = await asyncio.gather(*send_tasks, return_exceptions=True)
            success_count = sum(1 for result in results if result is True)
        
        self.logger.bind(tag=TAG).info(
            f"广播消息完成 - 目标连接: {len(send_tasks)}, 成功: {success_count}"
        )
        
        return success_count
    
    async def _send_to_connection(self, handler: ConnectionHandler, message: str) -> bool:
        """
        向单个连接发送消息
        
        Args:
            handler: 连接处理器
            message: 消息内容
            
        Returns:
            bool: 是否发送成功
        """
        try:
            if handler.websocket and handler.connected:
                await handler.websocket.send(message)
                return True
        except ConnectionClosed:
            # 连接已关闭，正常情况
            pass
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"发送消息到连接失败: {e}")
        
        return False
    
    async def shutdown(self) -> None:
        """
        优雅关闭服务器
        """
        self.logger.bind(tag=TAG).info("开始关闭WebSocket服务器...")
        
        # 通知所有连接即将关闭
        shutdown_message = {
            "type": "system",
            "status": "shutdown",
            "message": "服务器即将关闭"
        }
        
        await self.broadcast_message(shutdown_message)
        
        # 等待一段时间让消息发送完成
        await asyncio.sleep(1)
        
        # 关闭所有连接
        close_tasks = []
        for handler in self.active_connections.copy():
            close_tasks.append(handler._cleanup())
        
        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
        
        self.logger.bind(tag=TAG).info("WebSocket服务器已关闭")
