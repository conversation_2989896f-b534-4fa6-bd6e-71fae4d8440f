#!/usr/bin/env python3
"""
插件系统测试脚本
测试各种插件功能的调用和响应
"""
import asyncio
import json
import websockets
from websockets.exceptions import ConnectionClosed

async def test_plugin_functions():
    """测试插件功能"""
    # WebSocket服务器地址
    uri = "ws://localhost:8000/?device-id=test-plugins&client-id=plugin-test-001"
    
    print(f"🔌 开始测试插件系统功能")
    print(f"📡 连接到: {uri}")
    print("=" * 60)
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接建立成功!")
            
            # 等待欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {json.loads(welcome_msg)['type']}")
            
            # 等待AI组件初始化完成消息
            init_msg = await websocket.recv()
            init_data = json.loads(init_msg)
            if init_data.get("type") == "system" and init_data.get("status") == "ready":
                print("🤖 AI组件初始化完成")
            
            print("\n🎯 开始测试插件功能...")
            
            # 测试时间相关插件
            await test_time_functions(websocket)
            
            # 测试天气相关插件
            await test_weather_functions(websocket)
            
            # 测试计算相关插件
            await test_calculator_functions(websocket)
            
            print("\n✅ 所有插件测试完成!")
            
    except ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_time_functions(websocket):
    """测试时间相关功能"""
    print("\n⏰ 测试时间相关插件")
    
    time_queries = [
        "现在几点了？",
        "今天是几号？",
        "现在是什么时间？",
        "今天星期几？"
    ]
    
    for i, query in enumerate(time_queries, 1):
        print(f"  📤 测试 {i}: {query}")
        await send_text_and_wait_response(websocket, query)
        await asyncio.sleep(1)

async def test_weather_functions(websocket):
    """测试天气相关功能"""
    print("\n🌤️ 测试天气相关插件")
    
    weather_queries = [
        "北京今天天气怎么样？",
        "上海的天气如何？",
        "广州未来几天的天气预报",
        "深圳的空气质量怎么样？"
    ]
    
    for i, query in enumerate(weather_queries, 1):
        print(f"  📤 测试 {i}: {query}")
        await send_text_and_wait_response(websocket, query)
        await asyncio.sleep(1)

async def test_calculator_functions(websocket):
    """测试计算相关功能"""
    print("\n🧮 测试计算相关插件")
    
    calc_queries = [
        "帮我计算 2+3*4",
        "算一下 100-25",
        "计算 sin(30)",
        "帮我算算 sqrt(16)",
        "1公里等于多少米？"
    ]
    
    for i, query in enumerate(calc_queries, 1):
        print(f"  📤 测试 {i}: {query}")
        await send_text_and_wait_response(websocket, query)
        await asyncio.sleep(1)

async def send_text_and_wait_response(websocket, text):
    """发送文本消息并等待响应"""
    try:
        # 发送文本消息
        text_msg = {
            "type": "text",
            "content": text
        }
        await websocket.send(json.dumps(text_msg))
        
        # 等待响应
        response_received = False
        timeout_count = 0
        max_timeout = 10  # 最多等待10次
        
        while not response_received and timeout_count < max_timeout:
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                response_data = json.loads(response)
                
                response_type = response_data.get("type", "unknown")
                
                if response_type == "function_response":
                    # 插件直接响应
                    content = response_data.get("content", "")
                    function_results = response_data.get("function_results", [])
                    
                    print(f"    🔌 插件响应: {content}")
                    
                    # 显示详细的函数执行结果
                    for result in function_results:
                        func_name = result.get("function_name", "")
                        success = result.get("success", False)
                        if success:
                            print(f"    ✅ 函数 {func_name} 执行成功")
                        else:
                            print(f"    ❌ 函数 {func_name} 执行失败")
                    
                    response_received = True
                    
                elif response_type == "llm_start":
                    print(f"    🧠 LLM开始处理...")
                    
                elif response_type == "llm_stream":
                    token = response_data.get("token", "")
                    print(f"{token}", end="", flush=True)
                    
                elif response_type == "llm_complete":
                    complete_text = response_data.get("text", "")
                    print(f"\n    ✅ LLM响应完成: {complete_text}")
                    response_received = True
                    
                elif response_type == "response":
                    # 普通响应
                    content = response_data.get("content", "")
                    print(f"    💬 普通响应: {content}")
                    response_received = True
                    
                elif response_type == "error":
                    content = response_data.get("content", "")
                    print(f"    ❌ 错误响应: {content}")
                    response_received = True
                    
                else:
                    print(f"    📨 其他响应: {response_type}")
                    
            except asyncio.TimeoutError:
                timeout_count += 1
                if timeout_count >= max_timeout:
                    print(f"    ⏰ 响应超时")
                    break
            except json.JSONDecodeError:
                print(f"    ⚠️  收到非JSON响应")
                break
                
    except Exception as e:
        print(f"    ❌ 发送消息异常: {e}")

async def test_direct_function_calls():
    """测试直接函数调用（如果支持）"""
    print("\n🔧 测试直接函数调用")
    
    # 这里可以添加直接调用插件函数的测试
    # 例如通过特殊的消息格式直接调用函数
    pass

async def main():
    """主函数"""
    print("🔌 xiaozhi-server 插件系统测试工具")
    print("=" * 60)
    
    await test_plugin_functions()
    
    print("=" * 60)
    print("🎉 插件测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
