# xiaozhi-server 重建版配置文件
# 这是默认配置文件，用户自定义配置请放在 data/.config.yaml

# 服务器基本配置
server:
  # 服务器监听地址和端口
  ip: 0.0.0.0
  port: 8000
  # HTTP服务端口（用于OTA和视觉分析接口）
  http_port: 8003
  # WebSocket地址（用于设备连接）
  websocket: ws://localhost:8000/xiaozhi/v1/
  # 视觉分析接口地址
  vision_explain: http://localhost:8003/mcp/vision/explain
  # 时区偏移量
  timezone_offset: +8
  # 认证配置
  auth:
    enabled: false
    tokens: []

# 日志配置
log:
  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_level: INFO
  # 日志目录
  log_dir: tmp
  # 日志文件名
  log_file: server.log
  # 数据目录
  data_dir: data
  # 控制台日志格式
  log_format: "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>"
  # 文件日志格式
  log_format_file: "{time:YYYY-MM-DD HH:mm:ss} - {version}_{selected_module} - {name} - {level} - {extra[tag]} - {message}"
  # 选中的模块（用于日志标识）
  selected_module: "000000"

# 音频配置
audio:
  # 音频格式
  format: opus
  # 采样率
  sample_rate: 16000
  # 声道数
  channels: 1
  # 帧持续时间（毫秒）
  frame_duration: 60
  # 使用完音频文件后是否删除
  delete_audio: true

# 连接配置
connection:
  # 没有语音输入多久后断开连接（秒）
  close_connection_no_voice_time: 120
  # TTS请求超时时间（秒）
  tts_timeout: 10

# 功能开关
features:
  # 开启唤醒词加速
  enable_wakeup_words_response_cache: true
  # 开场是否回复唤醒词
  enable_greeting: true
  # 说完话是否开启提示音
  enable_stop_tts_notify: false
  # 提示音文件路径
  stop_tts_notify_voice: "config/assets/tts_notify.mp3"

# 唤醒词列表
wakeup_words:
  - "你好小智"
  - "嘿你好呀"
  - "你好小志"

# 退出指令
exit_commands:
  - "退出"
  - "关闭"

# AI模块选择配置
selected_module:
  VAD: SileroVAD
  ASR: MockASR
  LLM: MockLLM
  TTS: MockTTS
  Memory: NoMemory
  Intent: NoIntent

# VAD配置（语音活动检测）
VAD:
  SileroVAD:
    type: silero
    threshold: 0.5
    model_dir: models/silero-vad
    min_silence_duration_ms: 200

# ASR配置（语音识别）
ASR:
  MockASR:
    type: mock
    output_dir: tmp/

# LLM配置（大语言模型）
LLM:
  MockLLM:
    type: mock
    model_name: mock-model

# TTS配置（语音合成）
TTS:
  MockTTS:
    type: mock
    voice: mock-voice
    output_dir: tmp/

# Memory配置（记忆模块）
Memory:
  NoMemory:
    type: nomem

# Intent配置（意图识别）
Intent:
  NoIntent:
    type: nointent

# 角色设定
prompt: |
  你是小智，一个友善的AI助手。你会用简洁明了的方式回答用户的问题。

# 插件配置
plugins:
  # 插件基础配置
  enabled: true
  # 插件目录
  plugin_dir: plugins_func
