#!/usr/bin/env python3
"""
WebSocket客户端测试脚本
用于测试xiaozhi-server的WebSocket连接功能
"""
import asyncio
import json
import websockets
from websockets.exceptions import ConnectionClosed

async def test_websocket_connection():
    """测试WebSocket连接"""
    # WebSocket服务器地址
    uri = "ws://localhost:8000/?device-id=test-device-001&client-id=test-client-001"
    
    print(f"正在连接到: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接建立成功!")
            
            # 等待欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {welcome_msg}")
            
            # 解析欢迎消息
            try:
                welcome_data = json.loads(welcome_msg)
                session_id = welcome_data.get("session_id")
                print(f"🆔 Session ID: {session_id}")
            except json.JSONDecodeError:
                print("⚠️  欢迎消息不是JSON格式")
            
            # 发送ping消息
            ping_msg = {
                "type": "ping",
                "timestamp": 1234567890
            }
            await websocket.send(json.dumps(ping_msg))
            print(f"📤 发送Ping消息: {ping_msg}")
            
            # 等待pong响应
            pong_response = await websocket.recv()
            print(f"📨 收到Pong响应: {pong_response}")
            
            # 发送文本消息
            text_msg = {
                "type": "text",
                "content": "你好，这是一条测试消息"
            }
            await websocket.send(json.dumps(text_msg))
            print(f"📤 发送文本消息: {text_msg}")
            
            # 等待响应
            text_response = await websocket.recv()
            print(f"📨 收到文本响应: {text_response}")
            
            # 发送纯文本消息
            await websocket.send("这是一条纯文本消息")
            print("📤 发送纯文本消息: 这是一条纯文本消息")
            
            # 等待响应
            pure_text_response = await websocket.recv()
            print(f"📨 收到纯文本响应: {pure_text_response}")
            
            # 发送音频数据（模拟）
            audio_data = b"fake_audio_data_12345"
            await websocket.send(audio_data)
            print(f"📤 发送音频数据: {len(audio_data)} bytes")
            
            # 等待音频确认
            audio_ack = await websocket.recv()
            print(f"📨 收到音频确认: {audio_ack}")
            
            print("✅ 所有测试完成!")
            
    except ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_authentication():
    """测试认证功能"""
    print("\n🔐 测试认证功能...")
    
    # 测试没有device-id的连接
    try:
        uri = "ws://localhost:8000/"
        async with websockets.connect(uri) as websocket:
            response = await websocket.recv()
            print(f"📨 无device-id响应: {response}")
    except Exception as e:
        print(f"⚠️  无device-id连接异常: {e}")
    
    print("🔐 认证测试完成")

async def main():
    """主函数"""
    print("🚀 开始WebSocket连接测试")
    print("=" * 50)
    
    # 基本连接测试
    await test_websocket_connection()
    
    # 认证测试
    await test_authentication()
    
    print("=" * 50)
    print("🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
