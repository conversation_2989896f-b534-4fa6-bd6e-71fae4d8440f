"""
插件注册系统
提供插件注册、管理和执行的核心功能
"""
from enum import Enum
from typing import Dict, Any, Callable, Optional, List
from config.logger import setup_logging

TAG = __name__


class ToolType(Enum):
    """插件工具类型枚举"""
    NONE = (1, "调用完工具后，不做其他操作")
    WAIT = (2, "调用工具，等待函数返回")
    CHANGE_SYS_PROMPT = (3, "修改系统提示词，切换角色性格或职责")
    SYSTEM_CTL = (4, "系统控制，影响正常的对话流程，如退出、播放音乐等，需要传递conn参数")
    IOT_CTL = (5, "IOT设备控制，需要传递conn参数")
    MCP_CLIENT = (6, "MCP客户端")

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message


class Action(Enum):
    """插件执行动作枚举"""
    ERROR = (-1, "错误")
    NOTFOUND = (0, "没有找到函数")
    NONE = (1, "啥也不干")
    RESPONSE = (2, "直接回复")
    REQLLM = (3, "调用函数后再请求llm生成回复")

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message


class ActionResponse:
    """插件执行结果响应"""
    
    def __init__(self, action: Action, result: Any, response: Optional[str]):
        """
        初始化动作响应
        
        Args:
            action: 动作类型
            result: 动作产生的结果
            response: 直接回复的内容
        """
        self.action = action
        self.result = result
        self.response = response
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "action": self.action.name,
            "action_code": self.action.code,
            "result": self.result,
            "response": self.response
        }


class FunctionItem:
    """插件函数项"""
    
    def __init__(self, name: str, description: Dict[str, Any], func: Callable, tool_type: ToolType):
        """
        初始化函数项
        
        Args:
            name: 函数名称
            description: 函数描述（Function Call格式）
            func: 函数对象
            tool_type: 工具类型
        """
        self.name = name
        self.description = description
        self.func = func
        self.tool_type = tool_type
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "tool_type": self.tool_type.name,
            "tool_type_code": self.tool_type.code
        }


class FunctionRegistry:
    """函数注册表"""
    
    def __init__(self):
        """初始化函数注册表"""
        self.function_registry: Dict[str, FunctionItem] = {}
        self.logger = setup_logging()
        self.logger.bind(tag=TAG).info("函数注册表初始化完成")
    
    def register_function(self, name: str, func_item: FunctionItem) -> bool:
        """
        注册函数
        
        Args:
            name: 函数名称
            func_item: 函数项
            
        Returns:
            bool: 注册是否成功
        """
        try:
            self.function_registry[name] = func_item
            self.logger.bind(tag=TAG).info(f"函数 '{name}' 注册成功")
            return True
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"函数 '{name}' 注册失败: {e}")
            return False
    
    def unregister_function(self, name: str) -> bool:
        """
        注销函数
        
        Args:
            name: 函数名称
            
        Returns:
            bool: 注销是否成功
        """
        if name not in self.function_registry:
            self.logger.bind(tag=TAG).warning(f"函数 '{name}' 未找到，无法注销")
            return False
        
        try:
            self.function_registry.pop(name)
            self.logger.bind(tag=TAG).info(f"函数 '{name}' 注销成功")
            return True
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"函数 '{name}' 注销失败: {e}")
            return False
    
    def get_function(self, name: str) -> Optional[FunctionItem]:
        """
        获取函数
        
        Args:
            name: 函数名称
            
        Returns:
            Optional[FunctionItem]: 函数项，未找到返回None
        """
        return self.function_registry.get(name)
    
    def get_all_functions(self) -> Dict[str, FunctionItem]:
        """
        获取所有函数
        
        Returns:
            Dict[str, FunctionItem]: 所有函数字典
        """
        return self.function_registry.copy()
    
    def get_function_descriptions(self) -> List[Dict[str, Any]]:
        """
        获取所有函数的描述（用于Function Call）
        
        Returns:
            List[Dict[str, Any]]: 函数描述列表
        """
        return [func_item.description for func_item in self.function_registry.values()]
    
    def get_functions_by_type(self, tool_type: ToolType) -> Dict[str, FunctionItem]:
        """
        根据工具类型获取函数
        
        Args:
            tool_type: 工具类型
            
        Returns:
            Dict[str, FunctionItem]: 指定类型的函数字典
        """
        return {
            name: func_item 
            for name, func_item in self.function_registry.items() 
            if func_item.tool_type == tool_type
        }
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """
        获取注册表统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        type_counts = {}
        for func_item in self.function_registry.values():
            type_name = func_item.tool_type.name
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        return {
            "total_functions": len(self.function_registry),
            "function_names": list(self.function_registry.keys()),
            "type_distribution": type_counts
        }


# 全局函数注册表
global_function_registry = FunctionRegistry()


def register_function(name: str, description: Dict[str, Any], tool_type: ToolType = ToolType.WAIT):
    """
    函数注册装饰器
    
    Args:
        name: 函数名称
        description: 函数描述（Function Call格式）
        tool_type: 工具类型
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        # 创建函数项
        func_item = FunctionItem(name, description, func, tool_type)
        
        # 注册到全局注册表
        global_function_registry.register_function(name, func_item)
        
        return func
    
    return decorator


def get_global_registry() -> FunctionRegistry:
    """
    获取全局函数注册表
    
    Returns:
        FunctionRegistry: 全局函数注册表
    """
    return global_function_registry
