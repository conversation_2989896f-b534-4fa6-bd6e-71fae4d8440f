"""
插件加载器
负责动态加载和管理插件模块
"""
import os
import importlib
import pkgutil
from typing import List, Dict, Any, Optional
from config.logger import setup_logging
from .register import get_global_registry, FunctionRegistry

TAG = __name__


class PluginLoader:
    """插件加载器"""
    
    def __init__(self, plugin_dir: str = "plugins_func.functions"):
        """
        初始化插件加载器
        
        Args:
            plugin_dir: 插件目录
        """
        self.plugin_dir = plugin_dir
        self.logger = setup_logging()
        self.loaded_modules: List[str] = []
        self.registry = get_global_registry()
        
        self.logger.bind(tag=TAG).info(f"插件加载器初始化完成 - 插件目录: {plugin_dir}")
    
    def load_all_plugins(self) -> bool:
        """
        加载所有插件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.bind(tag=TAG).info("开始加载所有插件...")
            
            # 获取插件包
            try:
                package = importlib.import_module(self.plugin_dir)
                package_path = package.__path__
            except ImportError as e:
                self.logger.bind(tag=TAG).error(f"无法导入插件包 {self.plugin_dir}: {e}")
                return False
            
            # 遍历包内的所有模块
            loaded_count = 0
            for _, module_name, _ in pkgutil.iter_modules(package_path):
                full_module_name = f"{self.plugin_dir}.{module_name}"
                
                if self.load_plugin(full_module_name):
                    loaded_count += 1
            
            self.logger.bind(tag=TAG).info(f"插件加载完成 - 成功加载: {loaded_count} 个插件")
            
            # 输出注册表统计信息
            stats = self.registry.get_registry_stats()
            self.logger.bind(tag=TAG).info(f"函数注册统计: {stats}")
            
            return True
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载插件时发生异常: {e}")
            return False
    
    def load_plugin(self, module_name: str) -> bool:
        """
        加载单个插件
        
        Args:
            module_name: 模块名称
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 检查是否已加载
            if module_name in self.loaded_modules:
                self.logger.bind(tag=TAG).debug(f"模块 '{module_name}' 已加载，跳过")
                return True
            
            # 导入模块
            importlib.import_module(module_name)
            self.loaded_modules.append(module_name)
            
            self.logger.bind(tag=TAG).debug(f"模块 '{module_name}' 加载成功")
            return True
            
        except ImportError as e:
            self.logger.bind(tag=TAG).error(f"导入模块 '{module_name}' 失败: {e}")
            return False
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"加载模块 '{module_name}' 时发生异常: {e}")
            return False
    
    def reload_plugin(self, module_name: str) -> bool:
        """
        重新加载插件
        
        Args:
            module_name: 模块名称
            
        Returns:
            bool: 重新加载是否成功
        """
        try:
            # 重新导入模块
            if module_name in self.loaded_modules:
                module = importlib.import_module(module_name)
                importlib.reload(module)
                self.logger.bind(tag=TAG).info(f"模块 '{module_name}' 重新加载成功")
            else:
                # 如果未加载过，直接加载
                return self.load_plugin(module_name)
            
            return True
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"重新加载模块 '{module_name}' 失败: {e}")
            return False
    
    def unload_plugin(self, module_name: str) -> bool:
        """
        卸载插件（注意：Python中无法完全卸载模块）
        
        Args:
            module_name: 模块名称
            
        Returns:
            bool: 卸载是否成功
        """
        try:
            if module_name in self.loaded_modules:
                self.loaded_modules.remove(module_name)
                self.logger.bind(tag=TAG).info(f"模块 '{module_name}' 已从加载列表中移除")
                return True
            else:
                self.logger.bind(tag=TAG).warning(f"模块 '{module_name}' 未在加载列表中")
                return False
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"卸载模块 '{module_name}' 失败: {e}")
            return False
    
    def get_loaded_plugins(self) -> List[str]:
        """
        获取已加载的插件列表
        
        Returns:
            List[str]: 已加载的模块名称列表
        """
        return self.loaded_modules.copy()
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件信息
        
        Returns:
            Dict[str, Any]: 插件信息
        """
        registry_stats = self.registry.get_registry_stats()
        
        return {
            "plugin_dir": self.plugin_dir,
            "loaded_modules": self.loaded_modules,
            "loaded_count": len(self.loaded_modules),
            "registry_stats": registry_stats
        }


class PluginManager:
    """插件管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化插件管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = setup_logging()
        
        # 获取插件配置
        plugin_config = config.get("plugins", {})
        self.enabled = plugin_config.get("enabled", True)
        plugin_dir = plugin_config.get("plugin_dir", "plugins_func.functions")
        
        # 创建插件加载器
        self.loader = PluginLoader(plugin_dir)
        self.registry = get_global_registry()
        
        self.logger.bind(tag=TAG).info(f"插件管理器初始化完成 - 启用状态: {self.enabled}")
    
    async def initialize(self) -> bool:
        """
        初始化插件系统
        
        Returns:
            bool: 初始化是否成功
        """
        if not self.enabled:
            self.logger.bind(tag=TAG).info("插件系统已禁用")
            return True
        
        try:
            self.logger.bind(tag=TAG).info("开始初始化插件系统...")
            
            # 加载所有插件
            success = self.loader.load_all_plugins()
            
            if success:
                self.logger.bind(tag=TAG).info("插件系统初始化成功")
                
                # 输出可用函数列表
                functions = self.registry.get_all_functions()
                function_names = list(functions.keys())
                self.logger.bind(tag=TAG).info(f"可用插件函数: {function_names}")
            else:
                self.logger.bind(tag=TAG).error("插件系统初始化失败")
            
            return success
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"插件系统初始化异常: {e}")
            return False
    
    def get_function_descriptions_for_llm(self) -> List[Dict[str, Any]]:
        """
        获取用于LLM Function Call的函数描述
        
        Returns:
            List[Dict[str, Any]]: 函数描述列表
        """
        if not self.enabled:
            return []
        
        return self.registry.get_function_descriptions()
    
    def execute_function(self, function_name: str, **kwargs) -> Optional[Any]:
        """
        执行插件函数
        
        Args:
            function_name: 函数名称
            **kwargs: 函数参数
            
        Returns:
            Optional[Any]: 函数执行结果
        """
        if not self.enabled:
            self.logger.bind(tag=TAG).warning("插件系统已禁用，无法执行函数")
            return None
        
        try:
            # 获取函数项
            func_item = self.registry.get_function(function_name)
            if not func_item:
                self.logger.bind(tag=TAG).error(f"函数 '{function_name}' 未找到")
                return None
            
            # 执行函数
            self.logger.bind(tag=TAG).info(f"执行插件函数: {function_name}")
            result = func_item.func(**kwargs)
            
            self.logger.bind(tag=TAG).info(f"函数 '{function_name}' 执行完成")
            return result
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"执行函数 '{function_name}' 时发生异常: {e}")
            return None
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        plugin_info = self.loader.get_plugin_info()
        
        return {
            "enabled": self.enabled,
            "plugin_info": plugin_info,
            "config": self.config.get("plugins", {})
        }
