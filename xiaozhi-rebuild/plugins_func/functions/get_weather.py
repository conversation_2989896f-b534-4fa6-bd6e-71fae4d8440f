"""
天气查询插件
提供天气信息查询功能（模拟实现）
"""
import random
from typing import Dict, Any
from ..register import register_function, ToolType, ActionResponse, Action

# 函数描述（用于LLM Function Call）
FUNCTION_DESCRIPTION = {
    "type": "function",
    "function": {
        "name": "get_weather",
        "description": "查询指定城市的天气信息",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称，如：北京、上海、广州等"
                },
                "days": {
                    "type": "integer",
                    "description": "查询天数，1表示今天，3表示未来3天",
                    "enum": [1, 3, 7],
                    "default": 1
                }
            },
            "required": ["city"]
        }
    }
}

# 模拟天气数据
WEATHER_CONDITIONS = [
    {"condition": "晴", "icon": "☀️", "temp_range": (20, 30)},
    {"condition": "多云", "icon": "⛅", "temp_range": (15, 25)},
    {"condition": "阴", "icon": "☁️", "temp_range": (10, 20)},
    {"condition": "小雨", "icon": "🌦️", "temp_range": (8, 18)},
    {"condition": "中雨", "icon": "🌧️", "temp_range": (5, 15)},
    {"condition": "雷阵雨", "icon": "⛈️", "temp_range": (12, 22)},
    {"condition": "雪", "icon": "❄️", "temp_range": (-5, 5)},
]

CITIES = [
    "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", 
    "重庆", "西安", "天津", "青岛", "大连", "厦门", "苏州", "无锡"
]


@register_function("get_weather", FUNCTION_DESCRIPTION, ToolType.WAIT)
def get_weather(city: str, days: int = 1, **kwargs) -> ActionResponse:
    """
    查询天气信息
    
    Args:
        city: 城市名称
        days: 查询天数
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含天气信息的响应
    """
    try:
        # 验证城市名称
        if not city:
            return ActionResponse(Action.ERROR, None, "请提供城市名称")
        
        # 验证天数
        if days not in [1, 3, 7]:
            days = 1
        
        # 生成模拟天气数据
        weather_data = generate_mock_weather(city, days)
        
        # 格式化响应文本
        if days == 1:
            weather_info = weather_data[0]
            response_text = (
                f"{city}今天的天气：{weather_info['condition']} {weather_info['icon']}\n"
                f"温度：{weather_info['temperature']}°C\n"
                f"湿度：{weather_info['humidity']}%\n"
                f"风力：{weather_info['wind']}\n"
                f"空气质量：{weather_info['aqi_level']}"
            )
        else:
            response_text = f"{city}未来{days}天天气预报：\n"
            for i, weather_info in enumerate(weather_data):
                day_desc = "今天" if i == 0 else f"{i+1}天后"
                response_text += (
                    f"{day_desc}：{weather_info['condition']} {weather_info['icon']} "
                    f"{weather_info['temperature']}°C\n"
                )
        
        return ActionResponse(Action.RESPONSE, weather_data, response_text)
        
    except Exception as e:
        error_msg = f"查询天气失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)


def generate_mock_weather(city: str, days: int) -> list:
    """
    生成模拟天气数据
    
    Args:
        city: 城市名称
        days: 天数
        
    Returns:
        list: 天气数据列表
    """
    weather_list = []
    
    for day in range(days):
        # 随机选择天气条件
        weather_condition = random.choice(WEATHER_CONDITIONS)
        
        # 生成温度（在范围内随机）
        temp_min, temp_max = weather_condition["temp_range"]
        temperature = random.randint(temp_min, temp_max)
        
        # 生成其他天气参数
        humidity = random.randint(30, 90)
        wind_speed = random.randint(1, 8)
        wind_directions = ["北风", "南风", "东风", "西风", "东北风", "西北风", "东南风", "西南风"]
        wind_direction = random.choice(wind_directions)
        
        # 空气质量
        aqi_levels = ["优", "良", "轻度污染", "中度污染"]
        aqi_weights = [0.4, 0.4, 0.15, 0.05]  # 权重，优和良的概率更高
        aqi_level = random.choices(aqi_levels, weights=aqi_weights)[0]
        aqi_value = {
            "优": random.randint(0, 50),
            "良": random.randint(51, 100),
            "轻度污染": random.randint(101, 150),
            "中度污染": random.randint(151, 200)
        }[aqi_level]
        
        weather_info = {
            "city": city,
            "day": day,
            "condition": weather_condition["condition"],
            "icon": weather_condition["icon"],
            "temperature": temperature,
            "humidity": humidity,
            "wind": f"{wind_direction}{wind_speed}级",
            "wind_direction": wind_direction,
            "wind_speed": wind_speed,
            "aqi_level": aqi_level,
            "aqi_value": aqi_value,
            "date": f"第{day+1}天"
        }
        
        weather_list.append(weather_info)
    
    return weather_list


@register_function("get_air_quality", {
    "type": "function",
    "function": {
        "name": "get_air_quality",
        "description": "查询指定城市的空气质量信息",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称"
                }
            },
            "required": ["city"]
        }
    }
}, ToolType.WAIT)
def get_air_quality(city: str, **kwargs) -> ActionResponse:
    """
    查询空气质量
    
    Args:
        city: 城市名称
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含空气质量信息的响应
    """
    try:
        if not city:
            return ActionResponse(Action.ERROR, None, "请提供城市名称")
        
        # 生成模拟空气质量数据
        aqi_levels = ["优", "良", "轻度污染", "中度污染", "重度污染"]
        aqi_weights = [0.3, 0.4, 0.2, 0.08, 0.02]
        aqi_level = random.choices(aqi_levels, weights=aqi_weights)[0]
        
        aqi_ranges = {
            "优": (0, 50),
            "良": (51, 100),
            "轻度污染": (101, 150),
            "中度污染": (151, 200),
            "重度污染": (201, 300)
        }
        
        aqi_min, aqi_max = aqi_ranges[aqi_level]
        aqi_value = random.randint(aqi_min, aqi_max)
        
        # 生成各项污染物数据
        pollutants = {
            "PM2.5": random.randint(10, 100),
            "PM10": random.randint(20, 150),
            "SO2": random.randint(5, 50),
            "NO2": random.randint(10, 80),
            "O3": random.randint(20, 120),
            "CO": round(random.uniform(0.5, 3.0), 1)
        }
        
        air_quality_data = {
            "city": city,
            "aqi_value": aqi_value,
            "aqi_level": aqi_level,
            "pollutants": pollutants,
            "primary_pollutant": max(pollutants, key=pollutants.get),
            "health_advice": get_health_advice(aqi_level)
        }
        
        response_text = (
            f"{city}当前空气质量：\n"
            f"AQI指数：{aqi_value} ({aqi_level})\n"
            f"主要污染物：{air_quality_data['primary_pollutant']}\n"
            f"健康建议：{air_quality_data['health_advice']}"
        )
        
        return ActionResponse(Action.RESPONSE, air_quality_data, response_text)
        
    except Exception as e:
        error_msg = f"查询空气质量失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)


def get_health_advice(aqi_level: str) -> str:
    """
    根据空气质量等级获取健康建议
    
    Args:
        aqi_level: 空气质量等级
        
    Returns:
        str: 健康建议
    """
    advice_map = {
        "优": "空气质量令人满意，基本无空气污染，各类人群可正常活动。",
        "良": "空气质量可接受，但某些污染物可能对极少数异常敏感人群健康有较弱影响。",
        "轻度污染": "易感人群症状有轻度加剧，健康人群出现刺激症状。建议儿童、老年人及心脏病、呼吸系统疾病患者应减少长时间、高强度的户外锻炼。",
        "中度污染": "进一步加剧易感人群症状，可能对健康人群心脏、呼吸系统有影响。建议疾病患者避免长时间、高强度的户外锻炼，一般人群适量减少户外运动。",
        "重度污染": "心脏病和肺病患者症状显著加剧，运动耐受力降低，健康人群普遍出现症状。建议儿童、老年人和心脏病、肺病患者应停留在室内，停止户外运动，一般人群减少户外运动。"
    }
    
    return advice_map.get(aqi_level, "无相关建议")
