"""
计算器插件
提供基本的数学计算功能
"""
import math
import re
from typing import Dict, Any, Union
from ..register import register_function, ToolType, ActionResponse, Action

# 函数描述（用于LLM Function Call）
CALCULATOR_DESCRIPTION = {
    "type": "function",
    "function": {
        "name": "calculate",
        "description": "执行数学计算，支持基本运算、三角函数、对数等",
        "parameters": {
            "type": "object",
            "properties": {
                "expression": {
                    "type": "string",
                    "description": "数学表达式，如：'2+3*4'、'sin(30)'、'sqrt(16)'等"
                },
                "precision": {
                    "type": "integer",
                    "description": "结果精度（小数位数）",
                    "default": 6,
                    "minimum": 0,
                    "maximum": 15
                }
            },
            "required": ["expression"]
        }
    }
}


@register_function("calculate", CALCULATOR_DESCRIPTION, ToolType.WAIT)
def calculate(expression: str, precision: int = 6, **kwargs) -> ActionResponse:
    """
    执行数学计算
    
    Args:
        expression: 数学表达式
        precision: 结果精度
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含计算结果的响应
    """
    try:
        if not expression or not expression.strip():
            return ActionResponse(Action.ERROR, None, "请提供要计算的数学表达式")
        
        # 清理和预处理表达式
        cleaned_expr = clean_expression(expression)
        
        # 安全性检查
        if not is_safe_expression(cleaned_expr):
            return ActionResponse(Action.ERROR, None, "表达式包含不安全的内容")
        
        # 替换数学函数
        processed_expr = replace_math_functions(cleaned_expr)
        
        # 执行计算
        try:
            # 使用受限的命名空间进行计算
            allowed_names = {
                "__builtins__": {},
                "abs": abs,
                "round": round,
                "min": min,
                "max": max,
                "sum": sum,
                "pow": pow,
                # 数学常数
                "pi": math.pi,
                "e": math.e,
                "tau": math.tau,
                # 数学函数
                "sin": math.sin,
                "cos": math.cos,
                "tan": math.tan,
                "asin": math.asin,
                "acos": math.acos,
                "atan": math.atan,
                "sinh": math.sinh,
                "cosh": math.cosh,
                "tanh": math.tanh,
                "log": math.log,
                "log10": math.log10,
                "log2": math.log2,
                "exp": math.exp,
                "sqrt": math.sqrt,
                "ceil": math.ceil,
                "floor": math.floor,
                "factorial": math.factorial,
                "degrees": math.degrees,
                "radians": math.radians,
            }
            
            result = eval(processed_expr, allowed_names, {})
            
            # 处理结果
            if isinstance(result, (int, float)):
                if isinstance(result, float):
                    # 应用精度设置
                    if precision >= 0:
                        result = round(result, precision)
                
                # 格式化结果
                if isinstance(result, float) and result.is_integer():
                    formatted_result = str(int(result))
                else:
                    formatted_result = str(result)
                
                calculation_data = {
                    "original_expression": expression,
                    "processed_expression": processed_expr,
                    "result": result,
                    "formatted_result": formatted_result,
                    "precision": precision,
                    "type": type(result).__name__
                }
                
                response_text = f"计算结果：{expression} = {formatted_result}"
                
                return ActionResponse(Action.RESPONSE, calculation_data, response_text)
            else:
                return ActionResponse(Action.ERROR, None, f"计算结果类型不支持: {type(result)}")
                
        except ZeroDivisionError:
            return ActionResponse(Action.ERROR, None, "除零错误：不能除以零")
        except ValueError as e:
            return ActionResponse(Action.ERROR, None, f"数值错误: {str(e)}")
        except OverflowError:
            return ActionResponse(Action.ERROR, None, "数值溢出：结果太大")
        except Exception as e:
            return ActionResponse(Action.ERROR, None, f"计算错误: {str(e)}")
            
    except Exception as e:
        error_msg = f"计算失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)


def clean_expression(expression: str) -> str:
    """
    清理数学表达式
    
    Args:
        expression: 原始表达式
        
    Returns:
        str: 清理后的表达式
    """
    # 移除空格
    cleaned = expression.replace(" ", "")
    
    # 替换中文符号
    replacements = {
        "×": "*",
        "÷": "/",
        "（": "(",
        "）": ")",
        "，": ",",
        "。": ".",
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    return cleaned


def is_safe_expression(expression: str) -> bool:
    """
    检查表达式是否安全
    
    Args:
        expression: 表达式
        
    Returns:
        bool: 是否安全
    """
    # 危险关键词
    dangerous_keywords = [
        "import", "exec", "eval", "open", "file", "input", "raw_input",
        "__", "getattr", "setattr", "delattr", "hasattr", "globals", "locals",
        "dir", "vars", "callable", "compile", "exit", "quit"
    ]
    
    expression_lower = expression.lower()
    
    for keyword in dangerous_keywords:
        if keyword in expression_lower:
            return False
    
    # 只允许数字、运算符、括号、字母（函数名）、点号、逗号
    allowed_pattern = r'^[0-9+\-*/().,a-zA-Z_\s]+$'
    if not re.match(allowed_pattern, expression):
        return False
    
    return True


def replace_math_functions(expression: str) -> str:
    """
    替换数学函数的中文名称
    
    Args:
        expression: 表达式
        
    Returns:
        str: 替换后的表达式
    """
    # 中文函数名映射
    chinese_functions = {
        "正弦": "sin",
        "余弦": "cos",
        "正切": "tan",
        "反正弦": "asin",
        "反余弦": "acos",
        "反正切": "atan",
        "对数": "log",
        "常用对数": "log10",
        "自然对数": "log",
        "指数": "exp",
        "平方根": "sqrt",
        "开方": "sqrt",
        "阶乘": "factorial",
        "绝对值": "abs",
        "向上取整": "ceil",
        "向下取整": "floor",
        "四舍五入": "round",
    }
    
    result = expression
    for chinese, english in chinese_functions.items():
        result = result.replace(chinese, english)
    
    return result


@register_function("convert_unit", {
    "type": "function",
    "function": {
        "name": "convert_unit",
        "description": "单位转换，支持长度、重量、温度等常见单位",
        "parameters": {
            "type": "object",
            "properties": {
                "value": {
                    "type": "number",
                    "description": "要转换的数值"
                },
                "from_unit": {
                    "type": "string",
                    "description": "源单位，如：米、千米、公斤、摄氏度等"
                },
                "to_unit": {
                    "type": "string",
                    "description": "目标单位"
                }
            },
            "required": ["value", "from_unit", "to_unit"]
        }
    }
}, ToolType.WAIT)
def convert_unit(value: float, from_unit: str, to_unit: str, **kwargs) -> ActionResponse:
    """
    单位转换
    
    Args:
        value: 要转换的数值
        from_unit: 源单位
        to_unit: 目标单位
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含转换结果的响应
    """
    try:
        # 单位转换表（转换为基本单位的系数）
        unit_conversions = {
            # 长度单位（基本单位：米）
            "米": 1.0,
            "千米": 1000.0,
            "厘米": 0.01,
            "毫米": 0.001,
            "英尺": 0.3048,
            "英寸": 0.0254,
            "码": 0.9144,
            
            # 重量单位（基本单位：千克）
            "千克": 1.0,
            "公斤": 1.0,
            "克": 0.001,
            "吨": 1000.0,
            "磅": 0.453592,
            "盎司": 0.0283495,
            
            # 面积单位（基本单位：平方米）
            "平方米": 1.0,
            "平方千米": 1000000.0,
            "平方厘米": 0.0001,
            "公顷": 10000.0,
            "亩": 666.667,
        }
        
        # 温度转换（特殊处理）
        if from_unit in ["摄氏度", "华氏度", "开尔文"] or to_unit in ["摄氏度", "华氏度", "开尔文"]:
            result = convert_temperature(value, from_unit, to_unit)
        else:
            # 普通单位转换
            if from_unit not in unit_conversions:
                return ActionResponse(Action.ERROR, None, f"不支持的源单位: {from_unit}")
            
            if to_unit not in unit_conversions:
                return ActionResponse(Action.ERROR, None, f"不支持的目标单位: {to_unit}")
            
            # 转换为基本单位，再转换为目标单位
            base_value = value * unit_conversions[from_unit]
            result = base_value / unit_conversions[to_unit]
        
        # 格式化结果
        if isinstance(result, float) and result.is_integer():
            formatted_result = str(int(result))
        else:
            formatted_result = f"{result:.6f}".rstrip('0').rstrip('.')
        
        conversion_data = {
            "original_value": value,
            "from_unit": from_unit,
            "to_unit": to_unit,
            "result": result,
            "formatted_result": formatted_result
        }
        
        response_text = f"单位转换结果：{value} {from_unit} = {formatted_result} {to_unit}"
        
        return ActionResponse(Action.RESPONSE, conversion_data, response_text)
        
    except Exception as e:
        error_msg = f"单位转换失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)


def convert_temperature(value: float, from_unit: str, to_unit: str) -> float:
    """
    温度单位转换
    
    Args:
        value: 温度值
        from_unit: 源温度单位
        to_unit: 目标温度单位
        
    Returns:
        float: 转换后的温度值
    """
    # 先转换为摄氏度
    if from_unit == "摄氏度":
        celsius = value
    elif from_unit == "华氏度":
        celsius = (value - 32) * 5 / 9
    elif from_unit == "开尔文":
        celsius = value - 273.15
    else:
        raise ValueError(f"不支持的温度单位: {from_unit}")
    
    # 再从摄氏度转换为目标单位
    if to_unit == "摄氏度":
        return celsius
    elif to_unit == "华氏度":
        return celsius * 9 / 5 + 32
    elif to_unit == "开尔文":
        return celsius + 273.15
    else:
        raise ValueError(f"不支持的温度单位: {to_unit}")
