"""
时间查询插件
提供当前时间、日期等时间相关信息
"""
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any
from ..register import register_function, ToolType, ActionResponse, Action

# 函数描述（用于LLM Function Call）
FUNCTION_DESCRIPTION = {
    "type": "function",
    "function": {
        "name": "get_time",
        "description": "获取当前时间和日期信息",
        "parameters": {
            "type": "object",
            "properties": {
                "format": {
                    "type": "string",
                    "description": "时间格式，可选值：'datetime'(完整日期时间)、'date'(仅日期)、'time'(仅时间)、'timestamp'(时间戳)",
                    "enum": ["datetime", "date", "time", "timestamp"],
                    "default": "datetime"
                },
                "timezone_offset": {
                    "type": "integer",
                    "description": "时区偏移小时数，默认为+8（北京时间）",
                    "default": 8
                }
            },
            "required": []
        }
    }
}


@register_function("get_time", FUNCTION_DESCRIPTION, ToolType.WAIT)
def get_time(format: str = "datetime", timezone_offset: int = 8, **kwargs) -> ActionResponse:
    """
    获取当前时间
    
    Args:
        format: 时间格式
        timezone_offset: 时区偏移
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含时间信息的响应
    """
    try:
        # 创建指定时区
        tz = timezone(timedelta(hours=timezone_offset))
        now = datetime.now(tz)
        
        # 根据格式返回不同的时间信息
        if format == "datetime":
            time_str = now.strftime("%Y年%m月%d日 %H:%M:%S")
            weekday = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            result = f"现在是{time_str}，{weekday}"
            
        elif format == "date":
            date_str = now.strftime("%Y年%m月%d日")
            weekday = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            result = f"今天是{date_str}，{weekday}"
            
        elif format == "time":
            time_str = now.strftime("%H:%M:%S")
            result = f"现在的时间是{time_str}"
            
        elif format == "timestamp":
            timestamp = int(now.timestamp())
            result = f"当前时间戳是{timestamp}"
            
        else:
            result = "不支持的时间格式"
            return ActionResponse(Action.ERROR, None, result)
        
        # 构建详细结果
        detailed_result = {
            "formatted_time": result,
            "iso_format": now.isoformat(),
            "timestamp": int(now.timestamp()),
            "timezone": f"UTC{timezone_offset:+d}",
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "hour": now.hour,
            "minute": now.minute,
            "second": now.second,
            "weekday": now.weekday() + 1,  # 1-7，星期一为1
            "weekday_name": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
        }
        
        return ActionResponse(Action.RESPONSE, detailed_result, result)
        
    except Exception as e:
        error_msg = f"获取时间失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)


# 额外的时间相关函数
@register_function("get_date", {
    "type": "function",
    "function": {
        "name": "get_date",
        "description": "获取今天的日期信息",
        "parameters": {
            "type": "object",
            "properties": {
                "include_weekday": {
                    "type": "boolean",
                    "description": "是否包含星期信息",
                    "default": True
                }
            },
            "required": []
        }
    }
}, ToolType.WAIT)
def get_date(include_weekday: bool = True, **kwargs) -> ActionResponse:
    """
    获取今天的日期
    
    Args:
        include_weekday: 是否包含星期信息
        **kwargs: 其他参数
        
    Returns:
        ActionResponse: 包含日期信息的响应
    """
    try:
        # 使用北京时间
        tz = timezone(timedelta(hours=8))
        now = datetime.now(tz)
        
        date_str = now.strftime("%Y年%m月%d日")
        
        if include_weekday:
            weekday = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            result = f"今天是{date_str}，{weekday}"
        else:
            result = f"今天是{date_str}"
        
        detailed_result = {
            "date": date_str,
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "weekday": now.weekday() + 1,
            "weekday_name": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
            "is_weekend": now.weekday() >= 5  # 星期六、日为周末
        }
        
        return ActionResponse(Action.RESPONSE, detailed_result, result)
        
    except Exception as e:
        error_msg = f"获取日期失败: {str(e)}"
        return ActionResponse(Action.ERROR, None, error_msg)
